name: Deploy <PERSON><PERSON> Docker App

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: [self-hosted, duchung]

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Ensure .env exists
        run: |
          if [ ! -f .env ]; then cp .env.example .env; fi

      - name: Build Docker image
        run: |
          docker compose build

      - name: Stop old containers (if exists)
        run: |
          docker compose down || true

      - name: Start new containers
        run: |
          docker compose up -d 