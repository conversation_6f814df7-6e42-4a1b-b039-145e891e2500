import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useParams } from "react-router-dom";
import { ScrollToTop } from "@/components/ScrollToTop";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";
import AboutUs from "./pages/AboutUs";
import Course from "./pages/Course";
import CourseDetail from "./pages/CourseDetail";
import Teacher from "./pages/Teacher";
import TeacherDetail from "./pages/TeacherDetail";
import Blog from "./pages/Blog";
import BlogDetail from "./pages/BlogDetail";

const queryClient = new QueryClient();

// Wrapper component to handle dynamic routing
const BlogDetailWrapper: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  return <BlogDetail slug={slug} />;
};

const TeacherDetailWrapper: React.FC = () => {
  const { teacherId } = useParams<{ teacherId: string }>();
  return <TeacherDetail teacherId={teacherId} />;
};

const CourseDetailWrapper: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  return <CourseDetail courseId={courseId} />;
};

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollToTop />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/about-us" element={<AboutUs />} />
            <Route path="/courses" element={<Course />} />
            <Route
              path="/courses/:courseId"
              element={<CourseDetailWrapper />}
            />
            <Route path="/teacher" element={<Teacher />} />
            <Route
              path="/teacher/:teacherId"
              element={<TeacherDetailWrapper />}
            />
            <Route path="/blog" element={<Blog />} />
            <Route path="/blog/:slug" element={<BlogDetailWrapper />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
