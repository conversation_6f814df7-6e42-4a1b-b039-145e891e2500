import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ContactSection } from "@/components/ContactSection";
import {
  Star,
  MapPin,
  Mail,
  Phone,
  Award,
  BookOpen,
  ChevronRight,
  Users,
  ArrowLeft,
  Clock,
  Info,
} from "lucide-react";
import { enhancedTeachersData } from "@/data/teacherData";
import { useEffect, useState } from "react";
import { RegistrationModal } from "@/components/RegistrationModal";
import { getTeacherDetail, Teacher as TeacherType } from "@/api/service/teacher";

export default function TeacherDetail({ teacherId }: { teacherId?: string }) {
  const navigate = useNavigate();
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [teacher, setTeacher] = useState<TeacherType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!teacherId) return;
    setLoading(true);
    setError(null);
    getTeacherDetail(teacherId)
      .then((data) => setTeacher(data))
      .catch(() => {
        setError("Không thể tải thông tin giáo viên.");
        setTeacher(null);
      })
      .finally(() => setLoading(false));
  }, [teacherId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Đang tải thông tin giáo viên...</h1>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !teacher) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">
            {error || "Giáo viên không tồn tại"}
          </h1>
          <Button onClick={() => navigate("/teacher")}> <ArrowLeft className="w-4 h-4 mr-2" /> Quay lại danh sách giáo viên </Button>
        </div>
        <Footer />
      </div>
    );
  }

  // Chuẩn hóa lấy dữ liệu từ API detail mới
  const teacherName = teacher?.name || "đang cập nhật";
  const teacherSubjects = teacher?.subjects?.length ? teacher.subjects.join(", ") : "đang cập nhật";
  const teacherTitle = teacher?.title || "đang cập nhật";
  const teacherBio = teacher?.bio || "đang cập nhật";
  const teacherQualifications = Array.isArray(teacher?.qualifications) ? teacher.qualifications : [];
  const teacherAchievements = Array.isArray(teacher?.achievements) ? teacher.achievements : [];
  const teacherSpecializations = Array.isArray(teacher?.specializations) ? teacher.specializations : [];
  const teacherAddress = teacher?.address || "đang cập nhật";
  const teacherEmail = teacher?.mail || teacher?.mail || "đang cập nhật";
  const teacherPhone = teacher?.phone || "đang cập nhật";
  const teacherRating = teacher?.rating ?? "đang cập nhật";
  const teacherTotalRatings = teacher?.total_ratings ?? "đang cập nhật";

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Breadcrumbs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <a href="/" className="hover:text-blue-600 transition-colors">
              Trang chủ
            </a>
            <ChevronRight className="w-4 h-4" />
            <a
              href="/teacher"
              className="hover:text-blue-600 transition-colors"
            >
              Giáo viên
            </a>
            <ChevronRight className="w-4 h-4" />
            <span className="text-blue-600 font-medium">{teacherName}</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8">
        <div className="grid lg:grid-cols-12 gap-6 lg:gap-8">
          {/* Left Sidebar - Teacher Profile */}
          <div className="lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden lg:sticky lg:top-8">
              {/* Profile Image */}
              <div className="relative h-64 bg-gradient-to-br from-blue-50 to-blue-100">
                <img
                  src={teacher.image}
                  alt={teacher.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.src =
                      teacher.image;
                  }}
                />
              </div>

              {/* Teacher Info */}
              <div className="p-6">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  {teacherName}
                </h1>
                <p className="text-[#0070F4] font-medium mb-4">
                  {teacherTitle}
                </p>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-2 sm:gap-4 mb-6 p-3 sm:p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-xl sm:text-2xl font-bold text-[#0070F4] mb-1">
                      {teacherRating}
                    </div>
                    <div className="text-xs text-gray-600">Đánh giá</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl sm:text-2xl font-bold text-[#0070F4] mb-1">
                      {teacherTotalRatings}+
                    </div>
                    <div className="text-xs text-gray-600">Lượt đánh giá</div>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="space-y-3">
                  <p className="font-semibold text-gray-800 mb-3">Liên hệ</p>
                  <div className="flex items-center space-x-3 text-sm text-gray-600">
                    <MapPin className="w-4 h-4 text-[#0070F4] flex-shrink-0" />
                    <span>{teacherAddress}</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm text-gray-600">
                    <Mail className="w-4 h-4 text-[#0070F4] flex-shrink-0" />
                    <span className="break-all">
                      {teacherEmail}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm text-gray-600">
                    <Phone className="w-4 h-4 text-[#0070F4] flex-shrink-0" />
                    <span>{teacherPhone}</span>
                  </div>
                </div>

                {/* Contact Button */}
                <Button
                  className="w-full mt-6 bg-[#0070F4] hover:bg-[#0070F4]/90"
                  onClick={() => setShowRegistrationModal(true)}
                >
                  Liên hệ ngay
                </Button>
              </div>
            </div>
          </div>

          {/* Right Content Area */}
          <div className="lg:col-span-8 space-y-6">
            {/* About Section */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                Giới thiệu
              </h2>
              <p className="text-gray-600 leading-relaxed">{teacherBio}</p>
            </div>

            {/* Education & Qualifications */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                Bằng cấp & Chứng chỉ
              </h2>

              <ul className="space-y-3">
                {teacherQualifications.length > 0
                  ? teacherQualifications.map((qualification, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-[#0070F4] rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-600">{qualification}</span>
                      </li>
                    ))
                  : <li>đang cập nhật</li>
                }
              </ul>
            </div>

            {/* Achievements */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                Thành tích nổi bật
              </h2>

              <ul className="space-y-3">
                {teacherAchievements.length > 0
                  ? teacherAchievements.map((achievement, index) => (
                      <li key={index} className="flex items-start space-x-3">
                        <div className="w-2 h-2 bg-[#0070F4] rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-600">{achievement}</span>
                      </li>
                    ))
                  : <li>đang cập nhật</li>
                }
              </ul>
            </div>

            {/* Teaching Methods */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                Phương pháp giảng dạy
              </h2>

              <div className="grid md:grid-cols-2 gap-4">
                {teacherSpecializations.length > 0
                  ? teacherSpecializations.map((spec, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg"
                      >
                        <div className="w-2 h-2 bg-[#0070F4] rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700 font-normal">{spec}</span>
                      </div>
                    ))
                  : <div>đang cập nhật</div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Courses Section */}

      {/* Contact Section */}
      <div className="bg-[#1868DB] py-12 sm:py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
          <h2 className="text-2xl sm:text-3xl font-bold text-white mb-4">
            Bắt đầu hành trình học tập với giáo viên {teacherName}
          </h2>
          <p className="text-blue-100 mb-6 sm:mb-8 max-w-3xl mx-auto">
            Đăng ký ngay để được tư vấn chi tiết về phương pháp học tập và lộ
            trình phù hợp với bạn.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={() => setShowRegistrationModal(true)}
              className="bg-white text-[#0070F4] hover:bg-gray-100"
            >
              Đăng ký tư vấn miễn phí
            </Button>
            <Button
              size="lg"
              onClick={() => navigate("/teacher")}
              className="bg-white text-[#0070F4] hover:bg-gray-100"
            >
              Xem giáo viên khác
            </Button>
          </div>
        </div>
      </div>

      {/* <ContactSection /> */}
      <ContactSection />
      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
      <Footer />
    </div>
  );
}
