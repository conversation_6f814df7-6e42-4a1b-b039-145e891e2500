import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { ContactSection } from "@/components/ContactSection";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import {
  Award,
  BookOpen,
  Users,
  Star,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { RegistrationModal } from "@/components/RegistrationModal";
import {
  getTeachers,
  getFeaturedTeacher,
  Teacher as TeacherType,
} from "@/api/service/teacher";

const BACKGROUND_COLOR = "#EFF6FF";
const LABEL_COLOR = "#0070F4";
const teachersPageData = {
  hero: {
    title: "<PERSON><PERSON>i ngũ giáo viên <PERSON>",
    subtitle:
      "Gi<PERSON><PERSON> viên là yếu tố cốt lõi tại <PERSON>c <PERSON>ng ETC - gi<PERSON>u chuyên môn, thấu hiểu và luôn truyền cảm hứng cho học sinh.",
  },
  teacherTeams: {
    badge: "<PERSON><PERSON>i ngũ chuyên nghiệp",
    title: "<PERSON>ội ngũ giáo viên xuất sắc",
    subtitle: "Những chuyên gia hàng đầu với kinh nghiệm giảng dạy phong phú",
    teachers: [
      {
        name: "Nguyễn Nguyệt Nga",
        image:
          "https://images.unsplash.com/photo-1573496800808-56566a492b63?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Giảng dạy tại THPT Việt Đức."],
        experience: ["Nhiều học sinh đạt giải Nhất, Nhì HSG Ngữ văn."],
      },
      {
        name: "Nguyễn Thị Thu Hiền",
        image:
          "https://images.unsplash.com/photo-1511629091441-ee46146481b6?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Giảng dạy tại THPT Trần Phú Hoàn Kiếm"],
        experience: ["Nhiều thành tích ôn thi & bồi dưỡng HSG môn Ngữ văn"],
      },
      {
        name: "Mai Kim Bình",
        image:
          "https://images.unsplash.com/photo-1515073838964-4d4d56a58b21?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Giảng dạy tại THPT Lương Thế Vinh"],
        experience: ["Giải Nhất GV giỏi quận Hoàn Kiếm, TP Hà Nội"],
      },
      {
        name: "Nguyễn Thu Uyên",
        image:
          "https://images.unsplash.com/photo-1664382951821-8151535191e5?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Cử nhân Xuất sắc ĐH Sư phạm Hà Nội"],
        experience: ["Thạc sĩ Ngữ văn – ĐH Sư phạm Hà Nội"],
      },
      {
        name: "Phạm Bảo Anh",
        image:
          "https://images.unsplash.com/photo-1573496799652-408c2ac9fe98?w=400&h=400&fit=crop&crop=face",
        subjects: ["Tiếng Anh"],
        description: ["Giảng dạy tại THPT Việt Đức"],
        experience: ["Giải Ba Thiết kế bài giảng điện tử môn Tiếng Anh"],
      },
      {
        name: "Nguyễn Phương Lan",
        image:
          "https://images.unsplash.com/photo-1496499354870-877088652a58?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["23 năm kinh nghiệm giảng dạy"],
        experience: ["Nhà giáo tâm huyết sáng tạo Hà Nội"],
      },
      {
        name: "Ngô Thị Hông Liên",
        image:
          "https://images.unsplash.com/photo-1498090890888-3df9298e7b84?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["25 năm giảng dạy & luyện thi Đại học môn Ngữ văn"],
        experience: ["GV dạy giỏi, Nhà giáo Thủ đô tâm huyết sáng tạo"],
      },
      {
        name: "Trần Thị Mai Hương",
        image:
          "https://images.unsplash.com/photo-1515994034738-1f437c226687?w=400&h=400&fit=crop&crop=face",
        subjects: ["Hoá học"],
        description: ["Giảng dạy tại THPT Việt Đức"],
        experience: ["Giáo viên dạy giỏi cấp Thành phố"],
      },
    ],
  },
};

const defaultTeachers: TeacherType[] = [
  {
    id: 1,
    name: "Nguyễn Nguyệt Nga",
    image:
      "https://images.unsplash.com/photo-1573496800808-56566a492b63?w=400&h=400&fit=crop&crop=face",
    subjects: ["Ngữ văn"],
    description: ["Giảng dạy tại THPT Việt Đức."],
    experience: ["Nhiều học sinh đạt giải Nhất, Nhì HSG Ngữ văn."],
  },
  // ... (có thể thêm các giáo viên mẫu khác nếu muốn)
];

export default function Teacher() {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [teachers, setTeachers] = useState<TeacherType[]>(defaultTeachers);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [featuredTeacher, setFeaturedTeacher] = useState<TeacherType | null>(
    null
  );
  const [featuredLoading, setFeaturedLoading] = useState(true);
  const [featuredError, setFeaturedError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    setError(null);
    getTeachers()
      .then((data) => {
        if (mounted) setTeachers(data);
      })
      .catch(() => {
        setError("Không thể tải danh sách giáo viên. Hiển thị dữ liệu mẫu.");
        setTeachers(defaultTeachers);
      })
      .finally(() => setLoading(false));

    setFeaturedLoading(true);
    setFeaturedError(null);
    getFeaturedTeacher()
      .then((data) => {
        if (mounted) setFeaturedTeacher(data);
      })
      .catch(() => {
        setFeaturedError("Không thể tải thông tin giáo viên nổi bật.");
        setFeaturedTeacher(null);
      })
      .finally(() => setFeaturedLoading(false));
    return () => {
      mounted = false;
    };
  }, []);

  // Pagination constants
  const TEACHERS_PER_PAGE = 8;
  const totalTeachers = teachers.length;
  const totalPages = Math.ceil(totalTeachers / TEACHERS_PER_PAGE);

  // Pagination functions with scroll-to-teachers-section
  const scrollToTeachersSection = () => {
    const teachersSection = document.getElementById("teachers-section");
    if (teachersSection) {
      // Get the element's position relative to the document
      const elementRect = teachersSection.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;

      // Offset to account for fixed header (adjust as needed)
      const offset = 100;
      const scrollToPosition = absoluteElementTop - offset;

      window.scrollTo({
        top: scrollToPosition,
        behavior: "smooth",
      });
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      // Small delay to ensure DOM updates before scrolling
      setTimeout(() => {
        scrollToTeachersSection();
      }, 100);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      // Small delay to ensure DOM updates before scrolling
      setTimeout(() => {
        scrollToTeachersSection();
      }, 100);
    }
  };

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // Small delay to ensure DOM updates before scrolling
      setTimeout(() => {
        scrollToTeachersSection();
      }, 100);
    }
  };

  // Get current page teachers
  const getCurrentPageTeachers = () => {
    const startIndex = (currentPage - 1) * TEACHERS_PER_PAGE;
    const endIndex = startIndex + TEACHERS_PER_PAGE;
    return teachers.slice(startIndex, endIndex);
  };

  const currentTeachers = getCurrentPageTeachers();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Custom Hero Section */}
      <section className="relative section-padding container-padding bg-gradient-to-br from-blue-50 via-white to-blue-100 overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#38bdf8] to-[#0070F4] rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute top-20 left-1/4 w-32 h-32 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 blur-2xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-50 border border-blue-200 mb-6">
            <div className="w-2 h-2 bg-[#0070F4] rounded-full mr-3"></div>
            <span className="text-[#0070F4] font-medium text-sm">
              {" "}
              Đội ngũ chuyên nghiệp
            </span>
          </div>

          {/* Title with enhanced styling */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Đội ngũ giáo viên <span className="text-[#0070F4]">Đức Hưng</span>
          </h1>

          {/* Subtitle */}
          <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            Giáo viên là yếu tố cốt lõi tại Đức Hưng - giàu chuyên môn, thấu
            hiểu và luôn truyền cảm hứng cho học sinh.
          </p>
        </div>

        {/* Featured Teacher Section */}
        <section className="section-padding container-padding">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-3xl md:text-4xl font-bold mb-4">
                  Người dẫn dắt <span className="text-[#0070F4]">Đức Hưng</span>
                </h2>
                {featuredLoading ? (
                  <div>
                    <LoadingSpinner isVisible={true} message="Đang tải..." />;
                  </div>
                ) : featuredError ? (
                  <div className="mb-6 text-red-500">{featuredError}</div>
                ) : featuredTeacher ? (
                  <>
                    <div className="mb-6">
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">
                        {featuredTeacher.name}
                      </h3>
                      <p className="text-lg text-[#0070F4] font-medium">
                        {featuredTeacher.title}
                      </p>
                    </div>
                    <div className="space-y-4">
                      {(featuredTeacher.achievements || []).map(
                        (achievement, index) => (
                          <div
                            key={index}
                            className="flex items-start space-x-3"
                          >
                            <Award className="w-5 h-5 text-[#0070F4] mt-1 flex-shrink-0" />
                            <p className="text-gray-700 leading-relaxed">
                              {achievement}
                            </p>
                          </div>
                        )
                      )}
                    </div>
                  </>
                ) : null}
              </div>
              <div className="order-1 lg:order-2">
                <div className="relative">
                  <div className="w-full h-96 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl overflow-hidden shadow-lg">
                    <div className="w-full h-full flex items-center justify-center">
                      {featuredLoading ? (
                        <div>
                          <LoadingSpinner
                            isVisible={true}
                            message="Đang tải..."
                          />
                          ;
                        </div>
                      ) : featuredError || !featuredTeacher ? (
                        <img
                          src="../public/assets/teacher_1.png"
                          className="w-full h-full object-cover object-center"
                          loading="lazy"
                        />
                      ) : (
                        <img
                          src={featuredTeacher.image}
                          className="w-full h-full object-cover object-center"
                          loading="lazy"
                          onError={(e) => {
                            e.currentTarget.src =
                              "https://images.unsplash.com/photo-1573496800808-56566a492b63?w=400&h=400&fit=crop&crop=face";
                          }}
                        />
                      )}
                    </div>
                  </div>
                  <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-[#0070F4] rounded-full flex items-center justify-center shadow-lg">
                    <Star className="w-12 h-12 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </section>

      {/* Teacher Team Section */}
      <section
        id="teachers-section"
        className="section-padding container-padding bg-white"
      >
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              {teachersPageData.teacherTeams.badge}
            </span> */}

            <h2 className="text-3xl lg:text-5xl font-bold mb-4 leading-tight">
              Đội ngũ giáo viên <span className="text-gradient">xuất sắc</span>
            </h2>

            <p className="lg:text-xl md:text-xl text-gray-600 mx-auto font-normal max-w-3xl">
              {teachersPageData.teacherTeams.subtitle}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {currentTeachers.map((teacher) => (
              <div
                key={teacher.id}
                className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105 hover:border-[#0070F4] active:scale-95"
                onClick={() => navigate(`/teacher/${teacher.id}`)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    navigate(`/teacher/${teacher.id}`);
                  }
                }}
                aria-label={`Xem chi tiết giáo viên ${teacher.name}`}
              >
                <div className="h-48 relative overflow-hidden group">
                  <img
                    src={teacher.image}
                    alt={teacher.name}
                    className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-110"
                    loading="lazy"
                    onError={(e) => {
                      e.currentTarget.src =
                        "https://images.unsplash.com/photo-1573496800808-56566a492b63?w=400&h=400&fit=crop&crop=face";
                    }}
                  />
                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <span className="text-white font-medium text-sm bg-[#0070F4] px-3 py-1 rounded-full">
                      Xem chi tiết
                    </span>
                  </div>

                  {/* Subject badge on image */}
                  <span
                    className="absolute top-3 left-3 px-4 py-1 rounded-full text-sm font-semibold shadow-sm z-10 bg-white"
                    style={{
                      backgroundColor: BACKGROUND_COLOR,
                      color: LABEL_COLOR,
                      // border: `2px solid ${BORDER_COLOR}`,
                    }}
                  >
                    {teacher.subjects.map((subjects, subIndex) => (
                      <div
                        key={subIndex}
                        className="flex items-start space-x-2"
                      >
                        {subjects}
                      </div>
                    ))}
                  </span>
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">
                    {teacher.name}
                  </h3>

                  <div className="space-y-3 mb-4">
                    {teacher.description.map((description, subIndex) => (
                      <div
                        key={subIndex}
                        className="flex items-start space-x-2"
                      >
                        <BookOpen className="w-4 h-4 text-[#0070F4] mt-1 flex-shrink-0" />
                        <p className="text-sm text-gray-600 leading-relaxed">
                          {description}
                        </p>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-start space-x-2 mb-4">
                    <Users className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                    <p className="text-sm text-gray-600 leading-relaxed">
                      {teacher.experience}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="mt-12 sm:mt-16 flex flex-col items-center space-y-4 sm:space-y-6">
              {/* Pagination Navigation */}
              <div className="flex items-center space-x-2 sm:space-x-4">
                {/* Previous Button */}
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-lg border transition-all duration-300 text-sm sm:text-base ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Previous page"
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-1 sm:space-x-2">
                  {Array.from({ length: totalPages }, (_, index) => {
                    const pageNumber = index + 1;
                    // Show all pages if totalPages <= 5, otherwise show smart pagination
                    const shouldShow =
                      totalPages <= 5 ||
                      pageNumber === 1 ||
                      pageNumber === totalPages ||
                      Math.abs(pageNumber - currentPage) <= 1;

                    if (!shouldShow) {
                      // Show ellipsis for gaps
                      if (pageNumber === 2 && currentPage > 4) {
                        return (
                          <span key={pageNumber} className="px-2 text-gray-400">
                            ...
                          </span>
                        );
                      }
                      if (
                        pageNumber === totalPages - 1 &&
                        currentPage < totalPages - 3
                      ) {
                        return (
                          <span key={pageNumber} className="px-2 text-gray-400">
                            ...
                          </span>
                        );
                      }
                      return null;
                    }

                    return (
                      <button
                        key={pageNumber}
                        onClick={() => goToPage(pageNumber)}
                        className={`w-8 h-8 sm:w-10 sm:h-10 rounded-lg border transition-all duration-300 text-sm sm:text-base ${
                          pageNumber === currentPage
                            ? "bg-[#0070F4] text-white border-[#0070F4] shadow-md scale-110"
                            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400"
                        }`}
                        aria-label={`Go to page ${pageNumber}`}
                        aria-current={
                          pageNumber === currentPage ? "page" : undefined
                        }
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                </div>

                {/* Next Button */}
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-lg border transition-all duration-300 text-sm sm:text-base ${
                    currentPage === totalPages
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Next page"
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Contact Form Section */}
      <ContactSection />
      <Footer />
      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </div>
  );
}
