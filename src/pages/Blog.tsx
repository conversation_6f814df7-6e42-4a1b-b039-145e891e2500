import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { ContactSection } from "@/components/ContactSection";
import {
  Calendar,
  User,
  Tag,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { RegistrationModal } from "@/components/RegistrationModal";
import { PageHeader } from "@/components/PageHeader";
import { useQuery } from "@tanstack/react-query";
import {
  getBlogArticles,
  BlogArticle,
  BlogApiResponse,
} from "@/api/service/blog";

export default function News() {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const ARTICLES_PER_PAGE = 7;

  const { data, isLoading, error } = useQuery<BlogApiResponse, Error>({
    queryKey: ["blog-articles", currentPage, ARTICLES_PER_PAGE],
    queryFn: () => getBlogArticles(currentPage, ARTICLES_PER_PAGE),
    // keepPreviousData: true, // Remove or move to options if not supported
  });

  const articles = (data as BlogApiResponse)?.data || [];
  const totalPages = (data as BlogApiResponse)?.total_pages || 1;

  const openRegistrationModal = () => setShowRegistrationModal(true);

  // Pagination functions with scroll-to-featured-article with offset
  const scrollToFeaturedArticle = () => {
    const featuredArticleElement = document.getElementById("featured-article");
    if (featuredArticleElement) {
      const elementRect = featuredArticleElement.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;
      const scrollToPosition = Math.max(0, absoluteElementTop - 80);
      window.scrollTo({
        top: scrollToPosition,
        left: 0,
        behavior: "smooth",
      });
    } else {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth",
      });
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      setTimeout(() => {
        scrollToFeaturedArticle();
      }, 100);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      setTimeout(() => {
        scrollToFeaturedArticle();
      }, 100);
    }
  };

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setTimeout(() => {
        scrollToFeaturedArticle();
      }, 100);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Tin tức":
        return "bg-blue-100 text-blue-700 border-blue-200";
      case "Thành tích":
        return "bg-green-100 text-green-700 border-green-200";
      case "Sự kiện":
        return "bg-purple-100 text-purple-700 border-purple-200";
      case "Học bổng":
        return "bg-orange-100 text-orange-700 border-orange-200";
      case "Hợp tác":
        return "bg-teal-100 text-teal-700 border-teal-200";
      case "Cuộc thi":
        return "bg-red-100 text-red-700 border-red-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Removed PageHeader to eliminate "Tin tức Đức Hưng" title */}
      {/* Custom Hero Section */}
      <section className="relative section-padding container-padding bg-gradient-to-br from-blue-50 via-white to-blue-100 overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#38bdf8] to-[#0070F4] rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute top-20 left-1/4 w-32 h-32 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 blur-2xl"></div>
        </div>

        <div className="relative max-w-4xl mx-auto text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-50 border border-blue-200 mb-6">
            <div className="w-2 h-2 bg-[#0070F4] rounded-full mr-3"></div>
            <span className="text-[#0070F4] font-medium text-sm">
              {" "}
              Tin tức nổi bật
            </span>
          </div>

          {/* Title with enhanced styling */}
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Tin tức <span className="text-[#0070F4]">nổi bật</span>
          </h1>

          {/* Subtitle */}
          <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            Những thông tin mới nhất về hoạt động và thành tựu của Đức Hưng
          </p>
        </div>
      </section>
      {/* Featured News Section */}
      <section className="section-padding container-padding bg-gray-50">
        <div className="max-w-6xl mx-auto">
          {/* Loading/Error state */}
          {isLoading && (
            <div>
              <LoadingSpinner isVisible={true} message="Đang tải..." />;
            </div>
          )}
          {error && <div>Lỗi tải bài viết</div>}
          {/* Main Featured Article - Show first article of current page */}
          {articles.length > 0 && (
            <div id="featured-article" className="mb-12">
              <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="grid lg:grid-cols-2 gap-0">
                  <div className="h-64 lg:h-auto relative overflow-hidden">
                    <img
                      src={articles[0].image}
                      alt={articles[0].title}
                      className="w-full h-full object-cover"
                      loading="lazy"
                      onError={(e) => {
                        e.currentTarget.src =
                          "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop";
                      }}
                    />
                  </div>
                  <div className="p-8 lg:p-12 flex flex-col justify-center">
                    <div className="flex items-center space-x-4 mb-4">
                      <span
                        className={`px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(
                          articles[0].category
                        )}`}
                      >
                        {articles[0].category}
                      </span>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(articles[0].date)}</span>
                      </div>
                    </div>
                    <h3 className="text-xl lg:text-2xl font-semibold text-gray-800 mb-4">
                      {articles[0].title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {articles[0].excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <User className="w-4 h-4" />
                        <span>{articles[0].author}</span>
                      </div>
                      <Button
                        variant="outline"
                        className="group bg-[#0070F4] hover:bg-[#0059c9] hover:text-white text-white border-[#0070F4] hover:border-[#0059c9]"
                        onClick={() => {
                          window.scrollTo(0, 0);
                          window.location.href = `/blog/${articles[0].slug}`;
                        }}
                      >
                        Đọc thêm
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* News Grid - Show remaining articles from current page */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {articles.slice(1).map((article) => (
              <div
                key={article.id}
                className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300 h-full flex flex-col"
              >
                <div className="h-48 relative overflow-hidden flex-shrink-0">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-full object-cover"
                    loading="lazy"
                    onError={(e) => {
                      e.currentTarget.src =
                        "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop";
                    }}
                  />
                </div>

                <div className="p-6 flex flex-col flex-grow">
                  <div className="flex items-center justify-between mb-3">
                    <span
                      className={`px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(
                        article.category
                      )}`}
                    >
                      {article.category}
                    </span>
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(article.date)}</span>
                    </div>
                  </div>

                  <h3 className="text-lg font-bold text-gray-800 mb-3 line-clamp-2">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3 flex-grow">
                    {article.excerpt}
                  </p>

                  <div className="flex items-center justify-between mt-auto">
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <User className="w-3 h-3" />
                      <span>{article.author}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="group bg-[#0070F4] hover:text-white hover:bg-[#0059c9] text-white border-[#0070F4] hover:border-[#0059c9]"
                      onClick={() => {
                        window.scrollTo(0, 0);
                        window.location.href = `/blog/${article.slug}`;
                      }}
                    >
                      Đọc thêm
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="mt-16 flex flex-col items-center space-y-6">
              {/* Pagination Navigation */}
              <div className="flex items-center space-x-4">
                {/* Previous Button */}
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Previous page"
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-4">
                  {Array.from({ length: totalPages }, (_, index) => {
                    const pageNumber = index + 1;
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => goToPage(pageNumber)}
                        className={`w-10 h-10 rounded-lg border transition-all duration-300 ${
                          pageNumber === currentPage
                            ? "bg-[#0070F4] text-white border-[#0070F4] shadow-md scale-110"
                            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400"
                        }`}
                        aria-label={`Go to page ${pageNumber}`}
                        aria-current={
                          pageNumber === currentPage ? "page" : undefined
                        }
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                </div>

                {/* Next Button */}
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                    currentPage === totalPages
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Next page"
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Contact Form Section */}
      <ContactSection />

      <Footer />

      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </div>
  );
}
