import { Header } from "@/components/Header";
import { Hero } from "@/components/Hero";
import { Features } from "@/components/Features";
import { Courses } from "@/components/Courses";
import { CourseSchedule } from "@/components/CourseSchedule";
import { Success } from "@/components/Success";
import TestimonialsSection from "@/components/Testimonials";
import { CampusLocationsSection } from "@/components/CampusLocationsSection";
import { ContactSection } from "@/components/ContactSection";
import { Learning } from "@/components/Learning";
import { Footer } from "@/components/Footer";
import { useState } from "react";
import { RegistrationModal } from "../components/RegistrationModal";

const Index = () => {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);

  const openRegistrationModal = () => setShowRegistrationModal(true);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <Hero />
      <Features />
      <Courses />
      <Learning onOpenRegistration={openRegistrationModal} />
      <CourseSchedule />
      <Success />
      <TestimonialsSection onOpenRegistration={openRegistrationModal} />
      <CampusLocationsSection />
      <ContactSection />
      <div id="contact">
        <Footer />
      </div>
      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </div>
  );
};

export default Index;
