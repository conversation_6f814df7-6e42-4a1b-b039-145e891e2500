import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { ContactSection } from "@/components/ContactSection";
import { PageHeader } from "@/components/PageHeader";
import { useNavigate } from "react-router-dom";
import { LoadingSpinner } from "@/components/LoadingSpinner";

import {
  Star,
  Clock,
  Users,
  CheckCircle,
  ArrowRight,
  Check,
  TrendingUp,
  DollarSign,
  Trophy,
  BookOpen,
  Target,
  Zap,
  Award,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { RegistrationModal } from "@/components/RegistrationModal";
import { axiosInstance } from "@/api/config";
import { getCourses, Course as CourseType } from "@/api/service/course";
import { getActivities, Activity } from "@/api/service/activities";

// Unified colour for all course level badges
const BACKGROUND_COLOR = "#EFF6FF";
const LABEL_COLOR = "#0070F4";
const BORDER_COLOR = "#0070F4";

const coursesPageData = {
  hero: {
    title: "<PERSON>h<PERSON>a học chất lượng cao",
    subtitle: "Nền tảng giáo dục toàn diện",
    description:
      "Phương pháp giảng dạy tiên tiến, tạo môi trường học tập năng động, phù hợp cho mọi lứa tuổi, trình độ",
    details:
      "Tận tâm – Hiện đại – Hiệu quả: Nền tảng giúp học viên vươn xa trên hành trình tri thức.",
    image:
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop",
  },

  whyChoose: {
    badge: "Lợi thế vượt trội",
    title: "Điểm khác biệt của Đức Hưng",
    subtitle:
      "Những yếu tố tạo nên sự khác biệt và ưu việt của Đức Hưng trong lĩnh vực giáo dục",
    features: [
      {
        icon: Target,
        title: "Lộ trình học cá nhân hóa",
        description:
          "Đội ngũ tư vấn luôn sẵn sàng lắng nghe, thấu hiểu và đưa ra lộ trình phù hợp với mỗi học viên",
        highlight: "100% cá nhân hóa",
      },
      {
        icon: Zap,
        title: "Giáo viên hỗ trợ sát sao",
        description:
          "Giáo viên theo dõi sát sao tiến trình học tập từng bài học cụ thể và đưa ra những cách học tối ưu",
        highlight: "Hỗ trợ 24/7",
      },
      {
        icon: Award,
        title: "Tài liệu cập nhật liên tục",
        description:
          "Tài liệu được cập nhật thường xuyên theo xu hướng mới nhất và phù hợp với từng độ tuổi",
        highlight: "Luôn mới nhất",
      },
    ],
  },

  courses: [
    // ĐÃ XOÁ DATA MOCK
  ],

  activities: {
    badge: "Hoạt động học tập",
    title: "Hình ảnh các hoạt động tại Đức Hưng",
    subtitle:
      "Cùng ghi lại những khoảnh khắc đẹp nhất cùng những hoạt động học tập tại Đức Hưng",
    images: [
      "https://images.unsplash.com/photo-1577896851231-70ef18881754?w=400&h=400&fit=crop",
      "https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=400&h=400&fit=crop",
      "https://images.unsplash.com/photo-1596496050827-8299e0220de1?w=400&h=400&fit=crop",
      "https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=400&h=400&fit=crop",
      "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=400&fit=crop",
      "https://images.unsplash.com/photo-1509228468518-180dd4864904?w=400&h=400&fit=crop",
    ],
  },
};

export default function Course() {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [courses, setCourses] = useState<CourseType[]>(coursesPageData.courses);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const defaultActivities: Activity[] = [
    {
      id: 1,
      images: [
        "https://images.unsplash.com/photo-1577896851231-70ef18881754?w=400&h=400&fit=crop",
        "https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=400&h=400&fit=crop",
        "https://images.unsplash.com/photo-1596496050827-8299e0220de1?w=400&h=400&fit=crop",
        "https://images.unsplash.com/photo-1581726690015-c9861fa5057f?w=400&h=400&fit=crop",
        "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=400&fit=crop",
        "https://images.unsplash.com/photo-1509228468518-180dd4864904?w=400&h=400&fit=crop",
      ],
    },
  ];
  const [activities, setActivities] = useState<Activity[]>(defaultActivities);
  const [loadingActivities, setLoadingActivities] = useState(true);
  const [errorActivities, setErrorActivities] = useState<string | null>(null);
  const navigate = useNavigate();

  // Course title to ID mapping for navigation
  const handleCourseDetail = (courseId: string | number | undefined) => {
    if (courseId) {
      navigate(`/courses/${courseId}`);
    } else {
      setShowRegistrationModal(true);
    }
  };

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    setError(null);
    getCourses()
      .then((data) => {
        if (mounted) setCourses(data);
      })
      .catch(() => {
        setError("Không thể tải danh sách khoá học. Hiển thị dữ liệu mẫu.");
        setCourses(coursesPageData.courses);
      })
      .finally(() => setLoading(false));
    return () => {
      mounted = false;
    };
  }, []);

  useEffect(() => {
    let mounted = true;
    setLoadingActivities(true);
    setErrorActivities(null);
    getActivities()
      .then((data) => {
        if (mounted) {
          if (data.length > 0) {
            // Lấy activity có id lớn nhất
            const maxIdActivity = data.reduce(
              (max, cur) => (cur.id > max.id ? cur : max),
              data[0]
            );
            setActivities([maxIdActivity]);
          } else {
            setActivities(defaultActivities);
          }
        }
      })
      .catch(() => {
        setErrorActivities("Không thể tải hoạt động. Hiển thị dữ liệu mẫu.");
        setActivities(defaultActivities);
      })
      .finally(() => setLoadingActivities(false));
    return () => {
      mounted = false;
    };
  }, []);

  const openRegistrationModal = () => setShowRegistrationModal(true);

  // Pagination constants
  const COURSES_PER_PAGE = 6;
  const totalCourses = courses.length;
  const totalPages = Math.ceil(totalCourses / COURSES_PER_PAGE);

  // Pagination functions with scroll-to-courses-section
  const scrollToCoursesSection = () => {
    const coursesSection = document.getElementById("courses-section");
    if (coursesSection) {
      // Get the element's position relative to the document
      const elementRect = coursesSection.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;

      // Offset to account for fixed header (adjust as needed)
      const offset = 100;
      const scrollToPosition = absoluteElementTop - offset;

      window.scrollTo({
        top: scrollToPosition,
        behavior: "smooth",
      });
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      // Small delay to ensure DOM updates before scrolling
      setTimeout(() => {
        scrollToCoursesSection();
      }, 100);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      // Small delay to ensure DOM updates before scrolling
      setTimeout(() => {
        scrollToCoursesSection();
      }, 100);
    }
  };

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // Small delay to ensure DOM updates before scrolling
      setTimeout(() => {
        scrollToCoursesSection();
      }, 100);
    }
  };

  // Get current page courses
  const getCurrentPageCourses = () => {
    const startIndex = (currentPage - 1) * COURSES_PER_PAGE;
    const endIndex = startIndex + COURSES_PER_PAGE;
    return courses.slice(startIndex, endIndex);
  };

  const currentCourses = getCurrentPageCourses();

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Custom Hero Section */}
      <section className="relative section-padding container-padding bg-white">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden z-0">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#38bdf8] to-[#0070F4] rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute top-20 left-1/4 w-32 h-32 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 blur-2xl"></div>
        </div>
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Image Side */}
            <div className="relative animate-scale-in">
              <div className="relative h-fit rounded-2xl overflow-hidden order-2 lg:order-1">
                <img
                  src={coursesPageData.hero.image}
                  alt="Students learning together"
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            </div>

            {/* Content Side */}
            <div className="order-1 lg:order-2">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-50 border border-blue-200 mb-6">
                <div className="w-2 h-2 bg-[#0070F4] rounded-full mr-3"></div>
                <span className="text-[#0070F4] font-medium text-sm">
                  Chương trình nổi bật
                </span>
              </div>
              <h1 className="font-bold mb-4">
                <span className="block text-5xl md:text-5xl lg:text-6xl leading-[1.15] mb-2">
                  Khoá học
                </span>
                <span className="block text-5xl md:text-5xl lg:text-6xl leading-[1.15] mb-2 text-[#0070F4]">
                  chất lượng cao
                </span>
              </h1>

              <p className="text-base md:text-base lg:text-lg text-gray-600 leading-relaxed mb-4">
                {coursesPageData.hero.description}
              </p>

              <div className="bg-blue-50 border-l-4 border-[#0070F4] p-6 rounded-r-lg">
                <p className="text-base md:text-base lg:text-base text-gray-700 leading-relaxed italic">
                  {coursesPageData.hero.details}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Section */}
      <section className="section-padding container-padding">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              {coursesPageData.whyChoose.badge}
            </span> */}
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              <span className="text-[#0070F4]">Điểm khác biệt </span>
              của chúng tôi
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto">
              {coursesPageData.whyChoose.subtitle}
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            {coursesPageData.whyChoose.features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div key={index} className="relative group">
                  {/* Background decoration */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-50 rounded-3xl transform rotate-3 group-hover:rotate-6 transition-transform duration-300 opacity-50"></div>

                  {/* Main card */}
                  <div className="relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:-translate-y-2">
                    {/* Icon with background */}
                    <div className="relative mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#0070F4] to-[#38bdf8] rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      {/* Highlight badge */}
                      <span className="absolute -top-2 -right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-full shadow-sm">
                        {feature.highlight}
                      </span>
                    </div>

                    <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-[#0070F4] transition-colors duration-300">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {feature.description}
                    </p>

                    {/* Decorative element */}
                    <div className="absolute bottom-4 right-4 w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300"></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Courses Section */}
      <section
        id="courses-section"
        className="section-padding container-padding bg-gradient-to-br from-blue-50 via-white to-blue-50"
      >
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              Khóa học <span className="text-gradient">đạt chuẩn quốc gia</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
              Tất cả các khóa học đều tuân thủ tiêu chuẩn của Bộ Giáo dục & Đào
              tạo, phù hợp với đa dạng độ tuổi và trình độ học viên.
            </p>
          </div>
          {loading ? (
            <div>
              <LoadingSpinner isVisible={true} message="Đang tải..." />;
            </div>
          ) : error ? (
            <div className="text-center py-4 text-red-500">{error}</div>
          ) : null}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {currentCourses.map((course, index) => (
              <div
                key={index}
                className="course-card bg-white border border-gray-200 rounded-xl overflow-hidden flex flex-col w-full max-w-md mx-auto h-full min-h-[540px] transition-colors duration-150 hover:border-[#0070F4] focus-within:border-[#0070F4]"
              >
                {/* Image full width, only top border radius */}
                <div className="relative w-full h-48 min-h-[192px] max-h-[192px]">
                  <img
                    src={course.image}
                    alt={course.title}
                    className="object-cover w-full h-full"
                    loading="lazy"
                    style={{ minHeight: 192, maxHeight: 192 }}
                  />
                  {/* Subject badge on image, inside border */}
                  <span
                    className="absolute top-3 left-3 px-4 py-1 rounded-full text-sm font-semibold shadow-sm z-10 bg-white"
                    style={{
                      backgroundColor: BACKGROUND_COLOR,
                      color: LABEL_COLOR,
                      border: `2px solid ${BACKGROUND_COLOR}`,
                    }}
                  >
                    {course.level}
                  </span>
                </div>
                {/* Card body */}
                <div className="flex flex-col flex-1 p-6">
                  <h3 className="course-title text-2xl font-bold text-gray-900 font-inter mb-2 mt-1 line-clamp-2">
                    {course.title}
                  </h3>
                  <p className="course-description h-fit text-base text-gray-600 font-inter mb-4 line-clamp-3">
                    {course.description}
                  </p>
                  <ul className="course-features mb-4">
                    {course.features.map((feature, i) => (
                      <li
                        key={i}
                        className="flex items-center text-gray-800 text-sm mb-2 font-inter"
                      >
                        <Check className="w-4 h-4 text-primary mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="course-meta flex justify-between items-center text-sm text-gray-500 mb-4 font-inter">
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" aria-hidden="true" />
                      {course.duration}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="w-4 h-4" aria-hidden="true" />
                      {course.students}
                    </span>
                  </div>

                  <Button
                    onClick={() => handleCourseDetail(course.id)}
                    className="bg-[#0070F4] hover:bg-[#0059c9] text-white rounded-lg py-3 px-6 w-full text-base font-inter transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-[#0070F4] focus:ring-offset-2 mt-auto"
                    style={{ boxShadow: "none", border: "none" }}
                    tabIndex={0}
                    aria-label={`Xem chi tiết: ${course.title}`}
                  >
                    Xem chi tiết <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="mt-16 flex flex-col items-center space-y-6">
              {/* Pagination Navigation */}
              <div className="flex items-center space-x-4">
                {/* Previous Button */}
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Previous page"
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-4">
                  {Array.from({ length: totalPages }, (_, index) => {
                    const pageNumber = index + 1;
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => goToPage(pageNumber)}
                        className={`w-10 h-10 rounded-lg border transition-all duration-300 ${
                          pageNumber === currentPage
                            ? "bg-[#0070F4] text-white border-[#0070F4] shadow-md scale-110"
                            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400"
                        }`}
                        aria-label={`Go to page ${pageNumber}`}
                        aria-current={
                          pageNumber === currentPage ? "page" : undefined
                        }
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                </div>

                {/* Next Button */}
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                    currentPage === totalPages
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Next page"
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Activities Section */}
      <section className="section-padding container-padding">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              {coursesPageData.activities.badge}
            </span> */}
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              Hình ảnh các <span className="text-[#0070F4]">hoạt động</span> tại
              Đức Hưng
            </h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-4xl mx-auto">
              {coursesPageData.activities.subtitle}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {activities.length > 0 &&
              activities[0].images.map((image, index) => (
                <div
                  key={index}
                  className="aspect-square rounded-xl overflow-hidden hover:scale-105 transition-transform duration-300 shadow-sm"
                >
                  <img
                    src={image}
                    alt={`Hoạt động học tập ${index + 1}`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                </div>
              ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <ContactSection />

      <Footer />

      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </div>
  );
}
