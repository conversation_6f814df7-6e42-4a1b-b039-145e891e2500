import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { enhancedCoursesData } from "@/data/courseData";
import { ContactSection } from "@/components/ContactSection";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import {
  Star,
  Clock,
  Users,
  BookOpen,
  ChevronRight,
  ArrowLeft,
  Share2,
  ChevronLeft,
  ChevronDown,
  ChevronUp,
  Target,
  UserCheck,
  FileText,
  MessageSquare,
  Check,
  Award,
} from "lucide-react";
import { useState, useEffect } from "react";
import { RegistrationModal } from "@/components/RegistrationModal";
import { getCourseDetail } from "@/api/service/course";

export default function CourseDetail({ courseId }: { courseId?: string }) {
  const navigate = useNavigate();
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [activeTab, setActiveTab] = useState("curriculum");
  const [currentReviewPage, setCurrentReviewPage] = useState(1);
  const [expandedSections, setExpandedSections] = useState<Set<number>>(
    new Set([0])
  );
  const [course, setCourse] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log("useEffect run, courseId:", courseId);
    if (!courseId) return;
    setLoading(true);
    setError(null);
    getCourseDetail(courseId)
      .then((data) => {
        console.log("API response:", data);
        setCourse(data);
      })
      .catch((err) => {
        console.error("API error:", err);
        setError("Không thể tải thông tin khoá học.");
        setCourse(null);
      })
      .finally(() => setLoading(false));
  }, [courseId]);

  // Set default tab based on screen size (only on initial load)
  useEffect(() => {
    const setDefaultTab = () => {
      const isMobile = window.innerWidth < 1024;
      if (isMobile) {
        setActiveTab("description");
      } else {
        setActiveTab("curriculum");
      }
    };

    // Set initial tab only once
    setDefaultTab();
  }, []);

  // Reviews pagination constants and functions
  const REVIEWS_PER_PAGE = 5;
  const totalReviews = course?.reviews.length || 0;
  const totalReviewPages = Math.ceil(totalReviews / REVIEWS_PER_PAGE);

  const getCurrentPageReviews = () => {
    if (!course) return [];
    const startIndex = (currentReviewPage - 1) * REVIEWS_PER_PAGE;
    const endIndex = startIndex + REVIEWS_PER_PAGE;
    return course.reviews.slice(startIndex, endIndex);
  };

  const handleReviewPageChange = (page: number) => {
    setCurrentReviewPage(page);
    // Scroll to reviews section
    const reviewsSection = document.getElementById("reviews-section");
    if (reviewsSection) {
      reviewsSection.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const toggleSection = (sectionIndex: number) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(sectionIndex)) {
        newSet.delete(sectionIndex);
      } else {
        newSet.add(sectionIndex);
      }
      return newSet;
    });
  };

  // Đảm bảo chỉ truy cập course sau khi đã kiểm tra
  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <LoadingSpinner
          isVisible={true}
          message="Đang tải thông tin khoá học..."
        />
      </div>
    );
  }

  if (error || !course) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold text-black mb-4">
            {error || "Khóa học không tồn tại"}
          </h1>
          <Button onClick={() => navigate("/courses")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Quay lại danh sách khóa học
          </Button>
        </div>
        <Footer />
      </div>
    );
  }

  // Đến đây chắc chắn course đã có dữ liệu
  const shareUrl = `${window.location.origin}/courses/${course.id}`;
  const shareOnFacebook = () => {
    window.open(
      `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        shareUrl
      )}`,
      "_blank"
    );
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : "text-gray-400"
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Breadcrumbs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <nav className="flex items-center space-x-2 text-sm text-black">
            <a href="/" className="hover:text-blue-600 transition-colors">
              Trang chủ
            </a>
            <ChevronRight className="w-4 h-4" />
            <a
              href="/courses"
              className="hover:text-blue-600 transition-colors"
            >
              Khóa học
            </a>
            <ChevronRight className="w-4 h-4" />
            <span className="text-blue-600 font-medium">{course.title}</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative bg-gray-50 min-h-screen overflow-hidden">
        {/* Content Container */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 py-8">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Left Content - Course Info */}
            <div className="lg:col-span-2 space-y-8">
              {/* Course Header */}
              <div className="space-y-4">
                <div className="flex flex-wrap items-center gap-3">
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                    {course.level}
                  </Badge>
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                    {course.difficulty}
                  </Badge>
                </div>
                <h1 className="text-3xl lg:text-4xl font-bold leading-relaxed">
                  {course.title}
                </h1>

                <p className="text-sm md:text-base leading-relaxed">
                  {course.description}
                </p>

                {/* Course Stats */}
                <div className="flex flex-wrap items-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      {renderStars(course.rating)}
                    </div>
                    <span className="font-semibold">{course.rating}</span>
                    <span>({totalReviews} đánh giá)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    <span>{course.students}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span>{course.duration}</span>
                  </div>
                </div>
              </div>

              {/* What You'll Learn Section */}
              <div className="mb-6 bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                <h3 className="text-xl font-semibold text-foreground mb-4">
                  Những gì bạn sẽ học được
                </h3>
                <div className="space-y-3">
                  {course.learning_objectives.map((objective, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 text-sm text-black"
                    >
                      <Check className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                      <span className="leading-relaxed">{objective}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Course Preview */}
              <div className="relative bg-gray-900 rounded-lg overflow-hidden aspect-[4/3] sm:aspect-[16/10] lg:aspect-video">
                <img
                  src={course.image}
                  alt={course.title}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Tabs */}
              <div className="border-b border-gray-200">
                {/* Mobile Tab Navigation */}
                <nav className="flex space-x-4 overflow-x-auto scrollbar-hide lg:hidden">
                  {[
                    { id: "description", label: "Mô tả" },
                    { id: "curriculum", label: "Chương trình học" },
                    { id: "reviews", label: "Đánh giá" },
                  ].map((tab) => (
                    <button
                      key={`mobile-${tab.id}`}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors whitespace-nowrap flex-shrink-0 ${
                        activeTab === tab.id
                          ? "border-blue-600 text-blue-600"
                          : "border-transparent text-black hover:text-gray-900 hover:border-gray-300"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>

                {/* Desktop Tab Navigation */}
                <nav className="hidden lg:flex space-x-8">
                  {[
                    { id: "curriculum", label: "Chương trình học" },
                    { id: "reviews", label: "Đánh giá" },
                  ].map((tab) => (
                    <button
                      key={`desktop-${tab.id}`}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                        activeTab === tab.id
                          ? "border-blue-600 text-blue-600"
                          : "border-transparent text-black hover:text-gray-900 hover:border-gray-300"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              {activeTab === "curriculum" && (
                <div className="space-y-6">
                  {/* Curriculum Header */}
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                    <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold break-words">
                      Lộ trình học tập
                    </h2>
                    <div className="flex items-center justify-start sm:justify-end gap-2">
                      <span className="text-sm sm:text-base text-gray-500 whitespace-nowrap">
                        {course.curriculum.length} học phần •{" "}
                        {course.curriculum.reduce(
                          (total: number, section: any) =>
                            total + section.topics.length,
                          0
                        )}{" "}
                        bài học
                      </span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {course.curriculum.map((section, index) => {
                      const isExpanded = expandedSections.has(index);
                      return (
                        <div
                          key={index}
                          className="border border-gray-200 rounded-lg bg-white overflow-hidden shadow-sm"
                        >
                          <div
                            className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => toggleSection(index)}
                          >
                            <div className="flex sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
                              <div className="flex items-center gap-3 flex-1 min-w-0">
                                <h3 className="font-semibold text-gray-900 text-base sm:text-lg break-words flex-1 min-w-0">
                                  Phần {index + 1}: {section.module}
                                </h3>
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant="outline"
                                    className="text-xs px-2 py-1 flex-shrink-0"
                                  >
                                    {section.topics.length} bài học
                                  </Badge>
                                  {isExpanded ? (
                                    <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                                  ) : (
                                    <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                                  )}
                                </div>
                              </div>
                            </div>
                            <p className="text-sm text-black mt-2 leading-relaxed break-words">
                              {section.description}
                            </p>
                          </div>

                          {isExpanded && (
                            <div className="border-t border-gray-200 p-4 space-y-3">
                              {section.topics.map((topic, topicIndex) => (
                                <div
                                  key={topicIndex}
                                  className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 py-3 border-b border-gray-100 last:border-b-0"
                                >
                                  <div className="flex items-start gap-3 min-w-0 flex-1">
                                    <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                                      <BookOpen className="w-3 h-3 text-blue-600" />
                                    </div>
                                    <div className="min-w-0 flex-1">
                                      <span className="text-sm sm:text-base text-black break-words leading-relaxed">
                                        {topic.title}
                                      </span>
                                    </div>
                                  </div>
                                  <span className="text-sm text-gray-500 flex-shrink-0 ml-9 sm:ml-0 sm:mt-0.5">
                                    {topic.duration}
                                  </span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Reviews Tab */}
              {activeTab === "reviews" && (
                <div className="space-y-6" id="reviews-section">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                    <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold break-words">
                      Đánh giá từ học viên
                    </h2>
                    <div className="flex items-center justify-start sm:justify-end gap-2">
                      {/* Rating stars */}
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 sm:w-5 sm:h-5 ${
                              i < Math.floor(course.rating)
                                ? "text-yellow-400 fill-current"
                                : "text-gray-400"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="font-semibold text-sm sm:text-base">
                        {course.rating}
                      </span>
                      {/* Review count */}
                      <span className="text-sm sm:text-base text-black whitespace-nowrap">
                        ({totalReviews} đánh giá)
                      </span>
                    </div>
                  </div>

                  {/* Reviews Cards */}
                  <div className="space-y-4">
                    {getCurrentPageReviews().map((review, idx) => (
                      <div
                        key={idx}
                        className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 shadow-sm overflow-hidden"
                      >
                        <div className="flex items-start gap-3 sm:gap-4">
                          <img
                            src={
                              review.avatar ||
                              "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"
                            }
                            alt={review.student_name}
                            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0 overflow-hidden">
                            {/* Header with name and date */}
                            <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between gap-1 mb-2">
                              <h4 className="font-semibold text-sm sm:text-base text-black break-words line-clamp-2 min-w-0">
                                {review.student_name}
                              </h4>
                              <span className="text-xs sm:text-sm text-black flex-shrink-0 whitespace-nowrap">
                                {review.date}
                              </span>
                            </div>

                            {/* Rating */}
                            <div className="flex items-center gap-2 mb-3">
                              <div className="flex items-center flex-shrink-0">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`w-3 h-3 sm:w-4 sm:h-4 ${
                                      i < Number(review.rating)
                                        ? "text-yellow-400 fill-current"
                                        : "text-gray-400"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-xs sm:text-sm font-medium text-black flex-shrink-0">
                                {review.rating}/5
                              </span>
                            </div>

                            {/* Comment */}
                            <div className="min-w-0">
                              <p className="text-black leading-relaxed break-words word-wrap text-sm sm:text-base hyphens-auto">
                                {review.comment}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Pagination */}
                  {totalReviewPages > 1 && (
                    <div className="flex items-center justify-center gap-1 sm:gap-2 mt-8 px-4 overflow-x-auto">
                      <button
                        onClick={() =>
                          handleReviewPageChange(currentReviewPage - 1)
                        }
                        disabled={currentReviewPage === 1}
                        className="flex items-center justify-center px-2 sm:px-3 h-10 text-sm font-medium text-black bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
                      >
                        <ChevronLeft className="w-4 h-4 sm:mr-1" />
                        <span className="hidden sm:inline">Trước</span>
                      </button>

                      <div className="flex items-center gap-1 sm:gap-2">
                        {[...Array(totalReviewPages)].map((_, index) => {
                          const page = index + 1;
                          return (
                            <button
                              key={page}
                              onClick={() => handleReviewPageChange(page)}
                              className={`flex items-center justify-center px-2 sm:px-3 h-10 text-sm font-medium rounded-md flex-shrink-0 min-w-[40px] ${
                                currentReviewPage === page
                                  ? "bg-blue-600 text-white"
                                  : "text-black bg-white border border-gray-300 hover:bg-gray-50"
                              }`}
                            >
                              {page}
                            </button>
                          );
                        })}
                      </div>

                      <button
                        onClick={() =>
                          handleReviewPageChange(currentReviewPage + 1)
                        }
                        disabled={currentReviewPage === totalReviewPages}
                        className="flex items-center justify-center px-2 sm:px-3 h-10 text-sm font-medium text-black bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
                      >
                        <span className="hidden sm:inline">Sau</span>
                        <ChevronRight className="w-4 h-4 sm:ml-1" />
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Description Tab - Mobile Only */}
              {activeTab === "description" && (
                <div className="space-y-6 lg:hidden">
                  <div className="space-y-6">
                    {/* This Course Includes section */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        Khoá học này bao gồm
                      </h3>
                      <div className="space-y-3">
                        {course.course_includes.map((item, index) => {
                          const icons = [
                            Target,
                            UserCheck,
                            FileText,
                            MessageSquare,
                          ];
                          const IconComponent = icons[index];
                          return (
                            <div
                              key={index}
                              className="flex items-start gap-3 text-sm text-black"
                            >
                              <IconComponent className="w-4 h-4 text-primary mt-1 flex-shrink-0" />
                              <span className="leading-relaxed">{item}</span>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Course Information */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        Thông tin khoá học
                      </h3>
                      <div className="space-y-3">
                        <div className="flex text-sm">
                          <span className="w-20 font-semibold flex-shrink-0">
                            Hình thức:
                          </span>
                          <span className="text-black flex-1">
                            {course.course_info.format}
                          </span>
                        </div>
                        <div className="flex text-sm">
                          <span className="w-20 font-semibold flex-shrink-0">
                            Địa điểm:
                          </span>
                          <span className="text-black flex-1">
                            {course.course_info.location}
                          </span>
                        </div>
                        <div className="flex text-sm">
                          <span className="w-20 font-semibold flex-shrink-0">
                            Lịch học:
                          </span>
                          <span className="text-black flex-1">
                            {course.course_info.schedule}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Instructor Information - Mobile */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        Thông tin giảng viên
                      </h3>
                      <div className="space-y-4">
                        {/* Instructor Profile */}
                        <div className="flex items-center gap-3">
                          <img
                            src={course.instructor.image}
                            alt={course.instructor.name}
                            className="w-12 h-12 rounded-full object-cover flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <h5 className="font-semibold text-black text-sm">
                              {course.instructor.name}
                            </h5>
                            <p className="text-xs text-black">
                              {course.instructor.title}
                            </p>
                          </div>
                        </div>

                        {/* Instructor Stats */}
                        <div className="space-y-3">
                          <div className="flex items-center gap-3 text-sm">
                            <Star className="w-4 h-4 text-primary flex-shrink-0" />
                            <span className="text-black">
                              {course.instructor.rating} đánh giá
                            </span>
                          </div>
                          <div className="flex items-center gap-3 text-sm">
                            <Users className="w-4 h-4 text-primary flex-shrink-0" />
                            <span className="text-black">
                              {course.instructor.student_count}+ học sinh
                            </span>
                          </div>
                          <div className="flex items-center gap-3 text-sm">
                            <BookOpen className="w-4 h-4 text-primary flex-shrink-0" />
                            <span className="text-black">
                              {course.instructor.course_count}+ khoá học
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Registration Section */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                      <h3 className="text-xl font-semibold text-foreground mb-4">
                        Đăng ký khóa học
                      </h3>
                      <div className="space-y-3">
                        <Button
                          onClick={() => setShowRegistrationModal(true)}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-base font-semibold"
                        >
                          Đăng ký ngay
                        </Button>
                        <Button
                          variant="outline"
                          className="w-full py-3 text-base"
                          onClick={shareOnFacebook}
                        >
                          <Share2 className="w-4 h-4 mr-2" />
                          Chia sẻ
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Sidebar - Course Information - Desktop Only */}
            <div className="lg:col-span-1 hidden lg:block">
              <div className="bg-white border border-gray-200 rounded-lg p-6 sticky top-8 shadow-sm">
                {/* Course Includes */}
                <div className="space-y-4">
                  {/* This Course Includes section */}
                  <div className="space-y-4">
                    <h4 className="font-semibold text-gray-900">
                      Khóa học này bao gồm
                    </h4>
                    <div className="space-y-3">
                      {course.course_includes.map((item, index) => {
                        const icons = [
                          Target,
                          UserCheck,
                          FileText,
                          MessageSquare,
                        ];
                        const IconComponent = icons[index];
                        return (
                          <div
                            key={index}
                            className="flex items-start gap-3 text-sm text-black"
                          >
                            <IconComponent className="w-4 h-4 text-primary mt-1 flex-shrink-0" />
                            <span className="leading-relaxed">{item}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* Course Information */}
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="font-semibold text-gray-900 mb-4">
                      Thông tin khóa học:
                    </h4>
                    <div className="space-y-3">
                      <div className="flex text-sm">
                        <span className="w-20 text-foreground font-semibold flex-shrink-0">
                          Hình thức:
                        </span>
                        <span className="text-black flex-1">
                          {course.course_info.format}
                        </span>
                      </div>
                      <div className="flex text-sm">
                        <span className="w-20 text-foreground font-semibold flex-shrink-0">
                          Địa điểm:
                        </span>
                        <span className="text-black flex-1">
                          {course.course_info.location}
                        </span>
                      </div>
                      <div className="flex text-sm">
                        <span className="w-20 text-foreground font-semibold flex-shrink-0">
                          Lịch học:
                        </span>
                        <span className="text-black flex-1">
                          {course.course_info.schedule}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Instructor Information */}
                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <h4 className="font-semibold text-gray-900 mb-4">
                      Thông tin giáo viên
                    </h4>
                    <div className="space-y-4">
                      {/* Instructor Profile */}
                      <div className="flex items-center gap-3">
                        <img
                          src={course.instructor.image}
                          alt={course.instructor.name}
                          className="w-12 h-12 rounded-full object-cover flex-shrink-0"
                        />
                        <div className="flex-1 min-w-0">
                          <h5 className="font-semibold text-black text-sm">
                            {course.instructor.name}
                          </h5>
                          <p className="text-xs text-black">
                            {course.instructor.title}
                          </p>
                        </div>
                      </div>

                      {/* Instructor Stats */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-3 text-sm">
                          <Award className="w-4 h-4 text-primary flex-shrink-0" />
                          <span className="text-black">
                            {course.instructor.experience}
                          </span>
                        </div>
                        <div className="flex items-center gap-3 text-sm">
                          <Star className="w-4 h-4 text-primary flex-shrink-0" />
                          <span className="text-black">
                            {course.instructor.rating} sao đánh giá
                          </span>
                        </div>
                        <div className="flex items-center gap-3 text-sm">
                          <BookOpen className="w-4 h-4 text-primary flex-shrink-0" />
                          <span className="text-black">
                            {course.instructor.course_count}+ khoá học
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="mt-6 pt-6 border-t border-gray-200 space-y-3">
                    <Button
                      onClick={() => setShowRegistrationModal(true)}
                      className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-base font-semibold"
                    >
                      Đăng ký ngay
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full py-3 text-base"
                      onClick={shareOnFacebook}
                    >
                      <Share2 className="w-4 h-4 mr-2" />
                      Chia sẻ
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ContactSection />

      <Footer />

      {/* Registration Modal */}
      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </div>
  );
}
