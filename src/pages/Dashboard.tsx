import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Head<PERSON> } from "@/components/Header";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, BookOpen, User, LogOut } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface Course {
  id: string;
  title: string;
  description: string;
  duration_weeks: number;
  total_sessions: number;
}

interface Enrollment {
  id: string;
  current_session: number;
  progress_percentage: number;
  status: string;
  course: Course;
  test_results?: TestResult[];
}

interface TestResult {
  test_number: number;
  score: number;
  max_score: number;
  test_date: string;
}

export default function Dashboard() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      navigate("/");
      return;
    }

    setUser(session.user);
    await loadUserData(session.user.id);
  };

  const loadUserData = async (userId: string) => {
    try {
      // Load profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      setProfile(profileData);

      // Load enrollments with courses and test results
      const { data: enrollmentData } = await supabase
        .from('enrollments')
        .select(`
          *,
          courses:course_id (
            id,
            title,
            description,
            duration_weeks,
            total_sessions
          ),
          test_results (
            test_number,
            score,
            max_score,
            test_date
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active');

      // Transform the data to match our interface
      const transformedEnrollments = enrollmentData?.map((enrollment: any) => ({
        ...enrollment,
        course: enrollment.courses, // Rename courses to course
        courses: undefined // Remove the original courses property
      })).filter((enrollment: any) => enrollment.course) || [];

      setEnrollments(transformedEnrollments);
    } catch (error) {
      console.error('Error loading user data:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải dữ liệu người dùng",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
    navigate("/");
  };

  const getNextSession = (currentSession: number, totalSessions: number) => {
    if (currentSession >= totalSessions) return "Đã hoàn thành";
    
    // Mock schedule - in reality this would come from database
    const scheduleMap: { [key: number]: string } = {
      1: "Thứ 2, 19:00 - 20:30",
      9: "Thứ 4, 18:00 - 19:30 (Kiểm tra)",
      18: "Thứ 6, 19:00 - 20:30 (Kiểm tra)",
      27: "Thứ 2, 19:00 - 20:30 (Kiểm tra)",
      36: "Thứ 4, 18:00 - 19:30 (Thi cuối khóa)",
    };
    
    return scheduleMap[currentSession + 1] || "Thứ 2, 19:00 - 20:30";
  };

  const getStatusColor = (progressPercentage: number) => {
    if (progressPercentage >= 100) return "bg-green-100 text-green-800";
    if (progressPercentage > 0) return "bg-blue-100 text-blue-800";
    return "bg-yellow-100 text-yellow-800";
  };

  const getStatusText = (progressPercentage: number) => {
    if (progressPercentage >= 100) return "Hoàn thành";
    if (progressPercentage > 0) return "Đang học";
    return "Sắp bắt đầu";
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">
              Chào mừng, {profile?.name || user?.email}! 👋
            </h1>
            <p className="text-gray-600 mt-2">
              Dashboard học tập của bạn
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate("/profile")}
              className="flex items-center gap-2"
            >
              <User size={16} />
              Hồ sơ
            </Button>
            <Button
              variant="outline"
              onClick={handleLogout}
              className="flex items-center gap-2 text-red-600 border-red-600 hover:bg-red-50"
            >
              <LogOut size={16} />
              Đăng xuất
            </Button>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Courses Section */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">
              Khóa học của bạn
            </h2>
            
            {enrollments.length === 0 ? (
              <Card className="text-center p-8">
                <CardContent>
                  <BookOpen className="mx-auto mb-4 text-gray-400" size={48} />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">
                    Chưa có khóa học nào
                  </h3>
                  <p className="text-gray-500">
                    Liên hệ với chúng tôi để đăng ký khóa học phù hợp
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {enrollments.map((enrollment) => (
                  <Card key={enrollment.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{enrollment.course.title}</CardTitle>
                        <Badge className={getStatusColor(enrollment.progress_percentage)}>
                          {getStatusText(enrollment.progress_percentage)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <p className="text-gray-600 text-sm">{enrollment.course.description}</p>
                        
                        <div className="flex items-center text-sm text-gray-600">
                          <Clock className="mr-2" size={16} />
                          Thời lượng: {enrollment.course.duration_weeks} tuần ({enrollment.course.total_sessions} buổi)
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600">
                          <Calendar className="mr-2" size={16} />
                          Buổi học tiếp theo: {getNextSession(enrollment.current_session, enrollment.course.total_sessions)}
                        </div>
                        
                        <div>
                          <div className="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Tiến độ ({enrollment.current_session}/{enrollment.course.total_sessions})</span>
                            <span>{Math.round(enrollment.progress_percentage)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${enrollment.progress_percentage}%` }}
                            ></div>
                          </div>
                        </div>

                        {enrollment.test_results && enrollment.test_results.length > 0 && (
                          <div>
                            <h4 className="font-semibold text-gray-700 mb-2">Kết quả kiểm tra:</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {enrollment.test_results.map((result) => (
                                <div key={result.test_number} className="text-sm bg-gray-50 p-2 rounded">
                                  <span className="font-medium">Lần {result.test_number}:</span>
                                  <span className="ml-1">{result.score}/{result.max_score}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div>
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">
              Thông tin học tập
            </h2>
            
            {/* Quick Stats */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Thống kê</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tổng khóa học:</span>
                    <span className="font-semibold">{enrollments.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Đang học:</span>
                    <span className="font-semibold text-blue-600">
                      {enrollments.filter(e => e.progress_percentage > 0 && e.progress_percentage < 100).length}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Hoàn thành:</span>
                    <span className="font-semibold text-green-600">
                      {enrollments.filter(e => e.progress_percentage >= 100).length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card>
              <CardHeader>
                <CardTitle>Liên hệ hỗ trợ</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-semibold text-gray-700">Địa chỉ:</p>
                  <p className="text-sm text-gray-600">45B Lý Thường Kiệt, Hoàn Kiếm, Hà Nội</p>
                </div>
                <div>
                  <p className="font-semibold text-gray-700">Chi nhánh:</p>
                  <p className="text-sm text-gray-600">2 Cửa Bắc, Ba Đình, Hà Nội</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
