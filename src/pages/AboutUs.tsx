import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import { ContactSection } from "@/components/ContactSection";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import TestimonialsSection from "@/components/Testimonials";
import {
  BookOpen,
  Users,
  Target,
  Zap,
  Heart,
  TrendingUp,
  Award,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { RegistrationModal } from "@/components/RegistrationModal";
import { enhancedTeachersData } from "@/data/teacherData";
import { useQuery } from "@tanstack/react-query";
import { getTeachers, Teacher as TeacherType } from "@/api/service/teacher";
import {
  getTestimonials,
  Testimonial as TestimonialType,
} from "@/api/service/testimonials";

const BACKGROUND_COLOR = "#EFF6FF";
const LABEL_COLOR = "#0070F4";

const aboutPageData = {
  hero: {
    title: "<PERSON><PERSON> chúng tôi",
    subtitle: "Nền tảng giáo dục cho tương lai",
    description:
      "Chúng tôi đồng hành cùng học viên ở mọi giai đoạn phát triển, mang đến lộ trình học tập hiệu quả, linh hoạt và phù hợp với từng mục tiêu cá nhân.",
    details:
      "Đức Hưng theo đuổi triết lý 'Không bỏ lại học sinh' và 'Tiến học lễ, hậu học văn' để đồng hành cùng từng em trên con đường phát triển bản thân",
    image:
      "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=600&h=400&fit=crop",
  },

  statistics: {
    badge: "Kết quả nổi bật",
    title: "Thống kê về Đức Hưng",
    stats: [
      {
        number: "500+",
        label: "Học viên đã đăng ký thành công tại Đức Hưng",
      },
      { number: "100+", label: "Học viên đã đỗ vào các trường đại học uy tín" },
      { number: "90%", label: "Tỷ lệ học viên hoàn thành khóa học" },
      { number: "95%", label: "Tỷ lệ học viên gợi ý cho bạn bè của họ" },
    ],
  },

  philosophy: {
    badge: "Giá trị cốt lõi",
    title: "Triết lý của chúng tôi",
    subtitle:
      "Chúng tôi tin rằng giáo dục tốt sẽ tạo ra thế hệ tương lai tốt, và mỗi cá nhân xứng đáng được hỗ trợ tốt nhất",
    values: [
      {
        icon: Target,
        title: "Tận tâm",
        description:
          "Luôn đặt học viên lên hàng đầu, tận tâm với nghề, giúp đỡ học viên phát triển và thành công.",
      },
      {
        icon: Zap,
        title: "Sự phát triển",
        description:
          "Khuyến khích sự phát triển cả về kiến thức, kỹ năng và nhân cách của mỗi học viên.",
      },
      {
        icon: Heart,
        title: "Gắn kết cộng đồng",
        description:
          "Xây dựng một cộng đồng học tập mạnh mẽ, nơi mọi người cùng nhau phát triển.",
      },
    ],
  },

  teacherTeams: {
    badge: "Đội ngũ chuyên nghiệp",
    title: "Đội ngũ giáo viên Đức Hưng",
    subtitle: "Những chuyên gia hàng đầu với kinh nghiệm giảng dạy phong phú",
    teachers: [
      {
        name: "Nguyễn Nguyệt Nga",
        image:
          "https://images.unsplash.com/photo-1573496800808-56566a492b63?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Giảng dạy tại THPT Việt Đức."],
        experience: ["Nhiều học sinh đạt giải Nhất, Nhì HSG Ngữ văn."],
      },
      {
        name: "Nguyễn Thị Thu Hiền",
        image:
          "https://images.unsplash.com/photo-1511629091441-ee46146481b6?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Giảng dạy tại THPT Trần Phú Hoàn Kiếm"],
        experience: ["Nhiều thành tích ôn thi & bồi dưỡng HSG môn Ngữ văn"],
      },
      {
        name: "Mai Kim Bình",
        image:
          "https://images.unsplash.com/photo-1515073838964-4d4d56a58b21?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Giảng dạy tại THPT Lương Thế Vinh"],
        experience: ["Giải Nhất GV giỏi quận Hoàn Kiếm, TP Hà Nội"],
      },
      {
        name: "Nguyễn Thu Uyên",
        image:
          "https://images.unsplash.com/photo-1664382951821-8151535191e5?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["Cử nhân Xuất sắc ĐH Sư phạm Hà Nội"],
        experience: ["Thạc sĩ Ngữ văn – ĐH Sư phạm Hà Nội"],
      },
      {
        name: "Phạm Bảo Anh",
        image:
          "https://images.unsplash.com/photo-1573496799652-408c2ac9fe98?w=400&h=400&fit=crop&crop=face",
        subjects: ["Tiếng Anh"],
        description: ["Giảng dạy tại THPT Việt Đức"],
        experience: ["Giải Ba Thiết kế bài giảng điện tử môn Tiếng Anh"],
      },
      {
        name: "Nguyễn Phương Lan",
        image:
          "https://images.unsplash.com/photo-1496499354870-877088652a58?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["23 năm kinh nghiệm giảng dạy"],
        experience: ["Nhà giáo tâm huyết sáng tạo Hà Nội"],
      },
      {
        name: "Ngô Thị Hông Liên",
        image:
          "https://images.unsplash.com/photo-1498090890888-3df9298e7b84?w=400&h=400&fit=crop&crop=face",
        subjects: ["Ngữ văn"],
        description: ["25 năm giảng dạy & luyện thi Đại học môn Ngữ văn"],
        experience: ["GV dạy giỏi, Nhà giáo Thủ đô tâm huyết sáng tạo"],
      },
      {
        name: "Trần Thị Mai Hương",
        image:
          "https://images.unsplash.com/photo-1515994034738-1f437c226687?w=400&h=400&fit=crop&crop=face",
        subjects: ["Hoá học"],
        description: ["Giảng dạy tại THPT Việt Đức"],
        experience: ["Giáo viên dạy giỏi cấp Thành phố"],
      },
    ],
  },
};

export default function AboutUs() {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();

  // API: Teachers
  const {
    data: teachers = [],
    isLoading: teachersLoading,
    error: teachersError,
  } = useQuery<TeacherType[], Error>({
    queryKey: ["teachers"],
    queryFn: getTeachers,
  });
  // API: Testimonials
  const {
    data: testimonials = [],
    isLoading: testimonialsLoading,
    error: testimonialsError,
  } = useQuery<TestimonialType[], Error>({
    queryKey: ["testimonials"],
    queryFn: getTestimonials,
  });

  // Function to map teacher names to their IDs from enhancedTeachersData
  const getTeacherIdByName = (teacherName: string): string | null => {
    const teacher = teachers.find((t) => t.name === teacherName);
    return teacher ? String(teacher.id) : null;
  };

  // Function to handle teacher card click
  const handleTeacherClick = (teacherName: string) => {
    const teacherId = getTeacherIdByName(teacherName);
    if (teacherId) {
      navigate(`/teacher/${teacherId}`);
    } else {
      // Fallback: navigate to teacher list page
      navigate("/teacher");
    }
  };

  // Pagination constants
  const TEACHERS_PER_PAGE = 8;
  const totalTeachers = teachers.length;
  const totalPages = Math.ceil(totalTeachers / TEACHERS_PER_PAGE);

  // Pagination functions with scroll-to-teachers-section
  const scrollToTeachersSection = () => {
    const teachersSection = document.getElementById("teachers-section");
    if (teachersSection) {
      const elementRect = teachersSection.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;
      const offset = 100;
      const scrollToPosition = absoluteElementTop - offset;
      window.scrollTo({
        top: scrollToPosition,
        behavior: "smooth",
      });
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
      setTimeout(() => {
        scrollToTeachersSection();
      }, 100);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      setTimeout(() => {
        scrollToTeachersSection();
      }, 100);
    }
  };

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setTimeout(() => {
        scrollToTeachersSection();
      }, 100);
    }
  };

  // Get current page teachers from API
  const getCurrentPageTeachers = () => {
    const startIndex = (currentPage - 1) * TEACHERS_PER_PAGE;
    const endIndex = startIndex + TEACHERS_PER_PAGE;
    return teachers.slice(startIndex, endIndex);
  };

  const currentTeachers = getCurrentPageTeachers();
  const stats = [
    {
      icon: Users,
      number: "500+",
      label: "Học viên đã đăng ký thành công tại Đức Hưng",
      color: "text-purple-600",
    },
    {
      icon: Award,
      number: "100%",
      label: "Học viên đã đỗ vào các trường đại học uy tín",
      color: "text-blue-600",
    },
    {
      icon: TrendingUp,
      number: "90%",
      label: "Tỷ lệ học viên hoàn thành khóa học",
      color: "text-teal-600",
    },
    {
      icon: BookOpen,
      number: "95%",
      label: "Tỷ lệ học viên gợi ý cho bạn bè của họ",
      color: "text-yellow-600",
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section - Split Layout */}
      <section className="relative section-padding container-padding bg-white">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden z-0">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#38bdf8] to-[#0070F4] rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute top-20 left-1/4 w-32 h-32 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 blur-2xl"></div>
        </div>
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="flex flex-col lg:grid lg:grid-cols-2 gap-12 items-center">
            {/* Image Side */}
            <div className="h-72 border-2 border-gray-200 rounded-lg bg-white flex lg:h-[500px] rounded-2xl overflow-hidden order-1 lg:order-1">
              <img
                src="/assets/Logo.svg"
                alt="Học sinh đang học tập"
                className="w-full h-auto p-8"
              />
            </div>

            {/* Content Side */}
            <div className="order-2 lg:order-2">
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-50 border border-blue-200 mb-6">
                <div className="w-2 h-2 bg-[#0070F4] rounded-full mr-3"></div>
                <span className="text-[#0070F4] font-medium text-sm">
                  Về chúng tôi
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                {aboutPageData.hero.title}
              </h1>

              <h2 className="text-xl md:text-2xl text-[#0070F4] font-semibold mb-6">
                {aboutPageData.hero.subtitle}
              </h2>

              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                {aboutPageData.hero.description}
              </p>

              <div className="bg-blue-50 border-l-4 border-[#0070F4] p-6 rounded-r-lg">
                <p className="text-gray-700 leading-relaxed italic">
                  {aboutPageData.hero.details}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="section-padding container-padding bg-[#1868DB]">
        <div className="max-w-6xl mx-auto text-center">
          {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 rounded-full border border-white/30 text-sm font-medium text-white mb-6 shadow-sm">
            <span className="w-2 h-2 bg-white rounded-full"></span>
            {aboutPageData.statistics.badge}
          </span> */}
          <h2 className="text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
            Thành tựu về <span className="text-[#01FFCC]">Đức Hưng</span>
          </h2>

          <p className="text-lg md:text-xl text-white/90 mx-auto font-normal max-w-3xl mb-16">
            Những con số ấn tượng thể hiện chất lượng giáo dục tại Đức Hưng
          </p>

          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 lg:gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-3 sm:p-4 lg:p-6 hover:bg-white/20 transition-all duration-300 h-32 sm:h-40 lg:h-48 flex flex-col justify-center items-center">
                  <div className="flex justify-center mb-2 sm:mb-4">
                    <div className="bg-white/20 rounded-full p-2 sm:p-3">
                      <stat.icon
                        size={20}
                        className="text-white sm:w-7 sm:h-7"
                      />
                    </div>
                  </div>
                  <div className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1 sm:mb-2 lg:mb-3 text-[#01FFCC]">
                    {stat.number}
                  </div>
                  <div className="text-purple-100 text-xs sm:text-sm font-medium text-center px-1 sm:px-2 leading-tight">
                    {stat.label}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Philosophy Section - Card Design */}
      <section className="section-padding container-padding bg-gray-50 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-100 rounded-full opacity-50"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-blue-200 rounded-full opacity-30"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-50 rounded-full opacity-40"></div>

        <div className="max-w-6xl mx-auto relative">
          <div className="text-center mb-16">
            {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              {aboutPageData.philosophy.badge}
            </span> */}

            <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              Triết lý của <span className="text-gradient">chúng tôi</span>
            </h2>

            <p className="text-lg md:text-xl text-gray-600 mx-auto font-normal max-w-3xl">
              {aboutPageData.philosophy.subtitle}
            </p>
          </div>

          {/* Philosophy Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {aboutPageData.philosophy.values.map((value, index) => {
              const cardColors = [
                {
                  gradient: "from-blue-500 to-blue-600",
                  bg: "bg-blue-50",
                  border: "border-blue-200",
                  accent: "bg-blue-500",
                },
                {
                  gradient: "from-green-500 to-green-600",
                  bg: "bg-green-50",
                  border: "border-green-200",
                  accent: "bg-green-500",
                },
                {
                  gradient: "from-orange-500 to-orange-600",
                  bg: "bg-orange-50",
                  border: "border-orange-200",
                  accent: "bg-orange-500",
                },
              ];

              const colors = cardColors[index];

              return (
                <div
                  key={index}
                  className={`group relative bg-white rounded-2xl p-8 shadow-lg border-2 ${colors.border} hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden`}
                >
                  {/* Card Background Pattern */}
                  <div
                    className={`absolute top-0 right-0 w-32 h-32 ${colors.bg} rounded-full opacity-20 transform translate-x-8 -translate-y-8`}
                  ></div>
                  <div
                    className={`absolute bottom-0 left-0 w-24 h-24 ${colors.bg} rounded-full opacity-15 transform -translate-x-6 translate-y-6`}
                  ></div>

                  {/* Icon Section */}
                  <div className="relative mb-6 flex justify-center">
                    <div
                      className={`w-20 h-20 bg-gradient-to-br ${colors.gradient} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-6 transition-all duration-300`}
                    >
                      <value.icon className="w-8 h-8 text-white" />
                    </div>

                    {/* Floating accent dots */}
                    <div
                      className={`absolute -top-2 -right-2 w-4 h-4 ${colors.accent} rounded-full opacity-60 group-hover:scale-125 transition-transform duration-300`}
                    ></div>
                    <div
                      className={`absolute -bottom-1 -left-1 w-3 h-3 ${colors.accent} rounded-full opacity-40 group-hover:scale-110 transition-transform duration-300`}
                    ></div>
                  </div>

                  {/* Content Section */}
                  <div className="relative text-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors duration-300">
                      {value.title}
                    </h3>

                    <p className="text-gray-600 text-sm leading-relaxed mb-6">
                      {value.description}
                    </p>

                    {/* Bottom accent line */}
                    <div
                      className={`w-16 h-1 ${colors.accent} rounded-full mx-auto group-hover:w-20 transition-all duration-300`}
                    ></div>
                  </div>

                  {/* Hover glow effect */}
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${colors.gradient} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}
                  ></div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section
        id="teachers-section"
        className="section-padding container-padding bg-white"
      >
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              {aboutPageData.teacherTeams.badge}
            </span> */}
            <h2 className="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
              Đội ngũ giáo viên <span className="text-gradient">Đức Hưng</span>
            </h2>
            <p className="text-lg md:text-xl text-gray-600 mx-auto font-normal max-w-3xl">
              {aboutPageData.teacherTeams.subtitle}
            </p>
          </div>
          {/* Loading/Error state for teachers */}
          {teachersLoading && (
            <div>
              <LoadingSpinner isVisible={true} message="Đang tải..." />;
            </div>
          )}
          {teachersError && <div>Lỗi tải giáo viên</div>}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {currentTeachers.map((teacher: any, index: number) => (
              <div
                key={index}
                className="bg-white rounded-2xl border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-105 hover:border-[#0070F4] active:scale-95"
                onClick={() => handleTeacherClick(teacher.name)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    handleTeacherClick(teacher.name);
                  }
                }}
                aria-label={`Xem chi tiết giáo viên ${teacher.name}`}
              >
                <div className="h-48 relative overflow-hidden group">
                  <img
                    src={teacher.image}
                    alt={teacher.name}
                    className="w-full h-full object-cover object-center transition-transform duration-300 group-hover:scale-110"
                    loading="lazy"
                    onError={(e) => {
                      e.currentTarget.src =
                        "https://images.unsplash.com/photo-1573496800808-56566a492b63?w=400&h=400&fit=crop&crop=face";
                    }}
                  />
                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <span className="text-white font-medium text-sm bg-[#0070F4] px-3 py-1 rounded-full">
                      Xem chi tiết
                    </span>
                  </div>
                  {/* Subject badge on image */}
                  <span
                    className="absolute top-3 left-3 px-4 py-1 rounded-full text-sm font-semibold shadow-sm z-10 bg-white"
                    style={{
                      backgroundColor: BACKGROUND_COLOR,
                      color: LABEL_COLOR,
                    }}
                  >
                    {(teacher.subjects || []).map(
                      (subject: string, subIndex: number) => (
                        <div
                          key={subIndex}
                          className="flex items-start space-x-2"
                        >
                          {subject}
                        </div>
                      )
                    )}
                  </span>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-bold text-gray-800 mb-2">
                    {teacher.name}
                  </h3>
                  <div className="space-y-3 mb-4">
                    {(teacher.description || []).map(
                      (description: string, subIndex: number) => (
                        <div
                          key={subIndex}
                          className="flex items-start space-x-2"
                        >
                          <BookOpen className="w-4 h-4 text-[#0070F4] mt-1 flex-shrink-0" />
                          <p className="text-sm text-gray-600 leading-relaxed">
                            {description}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                  <div className="flex items-start space-x-2 mb-4">
                    <Users className="w-4 h-4 text-green-600 mt-1 flex-shrink-0" />
                    <p className="text-sm text-gray-600 leading-relaxed">
                      {(teacher.experience || []).join(", ")}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="mt-16 flex flex-col items-center space-y-6">
              {/* Pagination Navigation */}
              <div className="flex items-center space-x-4">
                {/* Previous Button */}
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                    currentPage === 1
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Previous page"
                >
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>

                {/* Page Numbers */}
                <div className="flex items-center space-x-4">
                  {Array.from({ length: totalPages }, (_, index) => {
                    const pageNumber = index + 1;
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => goToPage(pageNumber)}
                        className={`w-10 h-10 rounded-lg border transition-all duration-300 ${
                          pageNumber === currentPage
                            ? "bg-[#0070F4] text-white border-[#0070F4] shadow-md scale-110"
                            : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400"
                        }`}
                        aria-label={`Go to page ${pageNumber}`}
                        aria-current={
                          pageNumber === currentPage ? "page" : undefined
                        }
                      >
                        {pageNumber}
                      </button>
                    );
                  })}
                </div>

                {/* Next Button */}
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                    currentPage === totalPages
                      ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                  }`}
                  aria-label="Next page"
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                </button>
              </div>
            </div>
          )}
        </div>
      </section>
      {/* Testimonials Section */}
      <TestimonialsSection />

      {/* Contact Form Section - Using ContactSection from home page */}
      <ContactSection />
      <Footer />
      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </div>
  );
}
