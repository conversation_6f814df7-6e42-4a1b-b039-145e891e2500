import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import NotFound from "@/pages/NotFound";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { ContactSection } from "@/components/ContactSection";
import {
  Calendar,
  User,
  Clock,
  Facebook,
  Twitter,
  Linkedin,
  ChevronRight,
  Quote,
  ArrowRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import {
  getBlogArticles,
  getBlogArticleDetail,
  BlogArticle,
  getRelatedBlogArticles,
} from "@/api/service/blog";

interface BlogDetailProps {
  slug?: string;
}
const getCategoryColor = (category: string) => {
  switch (category) {
    case "Tin tức":
      return "bg-blue-100 text-blue-700 border-blue-200";
    case "Thành tích":
      return "bg-green-100 text-green-700 border-green-200";
    case "Sự kiện":
      return "bg-purple-100 text-purple-700 border-purple-200";
    case "Học bổng":
      return "bg-orange-100 text-orange-700 border-orange-200";
    case "Hợp tác":
      return "bg-teal-100 text-teal-700 border-teal-200";
    case "Cuộc thi":
      return "bg-red-100 text-red-700 border-red-200";
    default:
      return "bg-gray-100 text-gray-700 border-gray-200";
  }
};

export default function BlogDetail({ slug }: { slug?: string }) {
  // Lấy danh sách bài viết để tìm id theo slug
  const { data: listData, isLoading: listLoading } = useQuery<
    import("@/api/service/blog").BlogApiResponse,
    Error
  >({
    queryKey: ["blog-articles", 1, 100],
    queryFn: () => getBlogArticles(1, 100),
  });
  const article = listData?.data.find((a) => a.slug === slug);

  // Lấy chi tiết bài viết theo id
  const {
    data: detail,
    isLoading: detailLoading,
    error,
  } = useQuery<import("@/api/service/blog").BlogArticle | null, Error>({
    queryKey: ["blog-article-detail", article?.id ?? "no-id"],
    queryFn: async () => {
      if (!article?.id || typeof article.id !== "number") return null;
      try {
        const result = await getBlogArticleDetail(article.id);
        return result ?? null;
      } catch {
        return null;
      }
    },
    enabled: !!article?.id && typeof article.id === "number",
  });

  // Lấy related posts từ API
  const { data: relatedPosts = [], isLoading: relatedLoading } = useQuery<
    BlogArticle[],
    Error
  >({
    queryKey: ["related-posts", article?.id],
    queryFn: () =>
      article?.id ? getRelatedBlogArticles(article.id) : Promise.resolve([]),
    enabled: !!article?.id,
  });

  // Carousel state for related posts
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [count, setCount] = useState(0);
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (!api) return;
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);
    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  // Chỉ render khi đã có đủ dữ liệu cần thiết
  if (listLoading || detailLoading || relatedLoading)
    return <LoadingSpinner isVisible={true} message="Đang tải bài viết..." />;
  if (error) return <div>Lỗi tải bài viết</div>;
  if (!article || !article.id)
    return (
      <div>
        <NotFound />
      </div>
    );
  if (!detail || !detail.content) return <div>Bài viết không có nội dung</div>;

  // Render toàn bộ thông tin từ detail (API detail)
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      {/* Breadcrumbs */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <a href="/" className="hover:text-blue-600 transition-colors">
              Trang chủ
            </a>
            <ChevronRight className="w-4 h-4" />
            <a href="/blog" className="hover:text-blue-600 transition-colors">
              Tin tức
            </a>
            <ChevronRight className="w-4 h-4" />
            <span className="text-blue-600 font-medium">{detail.category}</span>
          </nav>
        </div>
      </div>
      {/* Hero Section */}
      <article className="max-w-4xl mx-auto px-6 py-8">
        {/* Badge - Centered */}
        <div className="text-center mb-6">
          <div
            className={`inline-flex items-center px-4 py-1 rounded-full border border-200 ${getCategoryColor(
              detail.category
            )}`}
          >
            <span className={`${getCategoryColor(detail.category)}`}>
              {detail.category}
            </span>
          </div>
        </div>
        {/* Title */}
        <h3 className="text-3xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6 lg:leading-snug leading-snug">
          {detail.title}
        </h3>
        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 mb-8 text-gray-600">
          <div className="flex items-center gap-2">
            <User className="w-4 h-4" />
            <span className="text-sm">{detail.author}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            <span className="text-sm">{detail.date}</span>
          </div>
          {detail.readTime && (
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span className="text-sm">{detail.readTime}</span>
            </div>
          )}
        </div>
        {/* Featured Image */}
        {detail.image && (
          <div className="mb-12 rounded-2xl overflow-hidden shadow-lg">
            <img
              src={detail.image}
              alt={detail.title}
              className="w-full h-64 md:h-96 object-cover"
              loading="lazy"
            />
          </div>
        )}
        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-12 ">
          <div
            className="mb-8 space-y-3 leading-normal"
            dangerouslySetInnerHTML={{ __html: detail.content }}
          />
        </div>
      </article>

      {/* Related Posts Section - Standalone */}
      {relatedPosts.length > 0 && (
        <section className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-6">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl text-[#0070F4] font-semibold mb-4">
                Bài viết mới nhất
              </h2>
              <p className="text-lg text-gray-600">
                Khám phá thêm những bài viết hữu ích khác từ Đức Hưng
              </p>
            </div>

            {/* Carousel Container */}
            <Carousel
              opts={{
                align: "start",
                loop: true,
              }}
              setApi={setApi}
              className="w-full relative"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {relatedPosts.map((post, index) => (
                  <CarouselItem
                    key={index}
                    className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3"
                  >
                    <div
                      className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-shadow duration-300 h-full flex flex-col cursor-pointer"
                      onClick={() => {
                        window.scrollTo(0, 0);
                        window.location.href = `/blog/${post.slug}`;
                      }}
                    >
                      <div className="h-48 relative overflow-hidden flex-shrink-0">
                        <img
                          src={post.image}
                          alt={post.title}
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      </div>

                      <div className="p-6 flex flex-col flex-grow">
                        <div className="flex items-center justify-between mb-3">
                          <span
                            className={`px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(
                              post.category
                            )}`}
                          >
                            {post.category}
                          </span>
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Calendar className="w-3 h-3" />
                            <span>{post.date}</span>
                          </div>
                        </div>

                        <h4 className="text-lg font-bold text-gray-800 mb-3 line-clamp-2">
                          {post.title}
                        </h4>
                        <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3 flex-grow">
                          {post.excerpt}
                        </p>

                        <div className="flex items-center justify-between mt-auto">
                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <User className="w-3 h-3" />
                            <span>{post.author}</span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="group bg-[#0070F4] hover:text-white hover:bg-[#0059c9] text-white border-[#0070F4] hover:border-[#0059c9]"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.scrollTo(0, 0);
                              window.location.href = `/blog/${post.slug}`;
                            }}
                          >
                            Đọc thêm
                            <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="carousel-nav-btn" />
              <CarouselNext className="carousel-nav-btn" />
            </Carousel>

            {/* Dot indicators */}
            {count > 1 && (
              <div className="flex justify-center mt-8 space-x-2">
                {Array.from({ length: count }).map((_, index) => (
                  <button
                    key={index}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index + 1 === current
                        ? "bg-[#0070F4] scale-110"
                        : "bg-gray-300 hover:bg-gray-400"
                    }`}
                    onClick={() => api?.scrollTo(index)}
                  />
                ))}
              </div>
            )}
          </div>
        </section>
      )}
      <ContactSection />
      <Footer />
    </div>
  );
}
