// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zykmsqfadcpocibypggm.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp5a21zcWZhZGNwb2NpYnlwZ2dtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMjUwNjksImV4cCI6MjA2NDYwMTA2OX0.allU7mZ60Qaj-JjfZKvgHYtZoea2WxwXBi0JdodmTjQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);