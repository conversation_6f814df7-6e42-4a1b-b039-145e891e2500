@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 47%; /* #0070F4 */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 47%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 60%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 210 100% 60%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 263 70% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 32;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply tracking-tight;
    font-weight: 600;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
    line-height: 1.3;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    line-height: 1.4;
  }

  h3 {
    @apply text-2xl md:text-3xl;
    line-height: 1.5;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent;
  }

  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:shadow-lg hover:shadow-primary/25;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-border;
  }

  .card-modern {
    @apply bg-card border border-border rounded-xl p-6 hover:shadow-lg hover:shadow-black/5 transition-all duration-300 hover:border-primary/20;
  }

  .nav-item {
    @apply text-muted-foreground hover:text-foreground transition-colors duration-200 font-medium;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  @keyframes scale-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
  }
  
  .animate-scale-pulse {
    animation: scale-pulse 600ms ease-in-out;
  }

  @keyframes icon-enter {
    0% {
      opacity: 0;
      transform: scale(0.8) translateY(20px) rotate(-5deg);
    }
    60% {
      transform: scale(1.05) translateY(-2px) rotate(1deg);
    }
    100% {
      opacity: 1;
      transform: scale(1) translateY(0) rotate(0deg);
    }
  }
  
  .animate-icon-enter {
    animation: icon-enter 0.6s ease-out forwards;
    animation-fill-mode: both;
  }

  /* Hide scrollbar for mobile tab navigation */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Text utilities for better mobile content handling */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .word-wrap {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  .hyphens-auto {
    hyphens: auto;
    -webkit-hyphens: auto;
    -moz-hyphens: auto;
  }

  /* Custom xs breakpoint for extra small screens */
  @media (min-width: 475px) {
    .xs\:flex-row {
      flex-direction: row;
    }
    .xs\:items-center {
      align-items: center;
    }
    .xs\:gap-3 {
      gap: 0.75rem;
    }
  }
}
