import { axiosInstance } from "@/api/config";

export interface Teacher {
  id: number;
  name: string;
  image: string;
  subjects: string[];
  description: string[];
  experience: string[];
  address?: string;
  phone?: string;
  mail?: string;
  title?: string;
  bio?: string;
  qualifications?: string[];
  achievements?: string[];
  rating?: number;
  total_ratings?: number;
  specializations?: string[];
  popular_courses?: string[];
}

export async function getTeachers(): Promise<Teacher[]> {
  const res = await axiosInstance.get("/api/teachers");
  if (res.data && Array.isArray(res.data.data)) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
}

export async function getTeacherDetail(id: string | number): Promise<Teacher> {
  const res = await axiosInstance.get(`/api/teachers/${id}`);
  if (res.data && res.data.data) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
}

export async function getFeaturedTeacher(): Promise<Teacher> {
  const res = await axiosInstance.get("/api/teachers/featured");
  if (res.data && res.data.data) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
} 