import { axiosInstance } from "@/api/config";

/**
 * Interface cho kho<PERSON> học dùng trong danh sách (list)
 */
export interface Course {
  id: string | number;
  level: string;
  title: string;
  description: string;
  duration: string;
  students: string;
  image: string;
  features: string[];
}

/**
 * Interface cho chi tiết kho<PERSON> họ<PERSON> (detail)
 */
export interface CourseDetail {
  id: string | number;
  level: string;
  title: string;
  description: string;
  duration: string;
  students: string;
  image: string;
  instructor: {
    name: string;
    title: string;
    image: string;
    experience: string[];
    rating: number;
    student_count: number | null;
    course_count: number | null;
  };
  difficulty: string;
  learning_objectives: string[];
  course_includes: string[];
  curriculum: Array<{
    module: string;
    topics: Array<{
      title: string;
      duration: string;
    }>;
    description: string;
  }>;
  rating: number;
  course_info: {
    format: string;
    location: string;
    schedule: string;
  };
  reviews: Array<{
    date: string;
    avatar: string;
    rating: number | string;
    comment: string;
    student_name: string;
  }>;
}

/**
 * <PERSON><PERSON><PERSON> danh sách kho<PERSON> họ<PERSON>
 */
export async function getCourses(): Promise<Course[]> {
  const res = await axiosInstance.get("/api/courses");
  if (res.data && Array.isArray(res.data.data)) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
}

/**
 * Lấy chi tiết khoá học theo id
 */
export async function getCourseDetail(id: string | number): Promise<CourseDetail> {
  const res = await axiosInstance.get(`/api/courses/${id}`);
  if (res.data && res.data.data) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
} 