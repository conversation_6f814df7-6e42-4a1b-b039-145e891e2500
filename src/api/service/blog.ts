import { axiosInstance } from "@/api/config";

export interface BlogArticle {
  id: number;
  title: string;
  excerpt: string;
  image: string;
  date: string;
  category: string;
  author: string;
  slug: string;
  metaDescription: string;
  readTime?: string;
  content?: string; // HTML string
}

export interface BlogApiResponse {
  data: BlogArticle[];
  total: number;
  page: number;
  page_size: number;
  total_pages: number;
}

export async function getBlogArticles(page = 1, page_size = 7): Promise<BlogApiResponse> {
  const res = await axiosInstance.get("/api/blog-articles", {
    params: { page, page_size }
  });
  return res.data;
}

export async function getBlogArticleDetail(id: number): Promise<BlogArticle | null> {
  const res = await axiosInstance.get(`/api/blog-articles/${id}`);
  return res.data?.data ?? null;
}

export async function getRelatedBlogArticles(id: number): Promise<BlogArticle[]> {
  const res = await axiosInstance.get(`/api/blog-articles/${id}/related`);
  return res.data?.data ?? [];
} 