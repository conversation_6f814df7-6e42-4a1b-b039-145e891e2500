import { axiosInstance } from "@/api/config";

export interface Testimonial {
  avatar: string;
  name: string;
  role: string;
  quote: string;
  badge: {
    label: string;
    color: string;
  };
}

export async function getTestimonials(): Promise<Testimonial[]> {
  const res = await axiosInstance.get("/api/testimonials");
  if (res.data && Array.isArray(res.data.data)) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
} 