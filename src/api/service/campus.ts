import { axiosInstance } from "@/api/config";

export interface Campus {
  id: number;
  name: string;
  address: string;
  phone: string;
  email: string;
  hours: string;
  mapLink: string;
}

export async function getCampuses(): Promise<Campus[]> {
  const res = await axiosInstance.get("/api/campuses");
  if (res.data && Array.isArray(res.data.data)) {
    return res.data.data;
  }
  throw new Error("Invalid API response");
} 