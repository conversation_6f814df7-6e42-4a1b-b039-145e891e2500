import React from "react";

interface LoadingSpinnerProps {
  /**
   * Optional loading message text
   * @default "Đang tải..."
   */
  message?: string;
  /**
   * Controls component visibility
   */
  isVisible: boolean;
  /**
   * Optional additional CSS classes
   */
  className?: string;
}

/**
 * Reusable loading spinner component with full-screen overlay
 *
 * @param message - Custom loading message (defaults to "Đang tải...")
 * @param isVisible - Controls whether the spinner is shown
 * @param className - Additional CSS classes for customization
 */
export function LoadingSpinner({
  message = "Đang tải...",
  isVisible,
  className = "",
}: LoadingSpinnerProps) {
  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 bg-white flex flex-col items-center justify-center z-50 ${className}`}
    >
      <svg
        className="w-16 h-16 animate-spin text-[#0078F0]"
        viewBox="0 0 100 101"
        fill="none"
        aria-label="Loading spinner"
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908
      C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908Z"
          fill="currentColor"
          opacity="0.2"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539
      C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289
      C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873
      C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071
      C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552
      C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758
      C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentColor"
        />
      </svg>
      <span className="mt-6 text-lg text-[#0078F0] font-normal tracking-wide animate-pulse">
        {message}
      </span>
    </div>
  );
}

// Default export for flexibility
export default LoadingSpinner;

/*
Usage Examples:

1. Basic usage with default message:
   <LoadingSpinner isVisible={loading} />

2. Custom message:
   <LoadingSpinner
     isVisible={loading}
     message="Đang tải khóa học..."
   />

3. With additional styling:
   <LoadingSpinner
     isVisible={loading}
     message="Đang tải bài viết..."
     className="bg-gray-50"
   />

4. Different contexts:
   - Course loading: message="Đang tải khóa học..."
   - Blog loading: message="Đang tải bài viết..."
   - User data: message="Đang tải thông tin..."
   - Form submission: message="Đang xử lý..."
*/
