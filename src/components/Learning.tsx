import { Play, Download, Smartphone } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

export function Learning({
  onOpenRegistration,
}: {
  onOpenRegistration?: () => void;
}) {
  return (
    <section className="py-8 sm:py-12 lg:py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-16 mx-4 my-4 items-center">
          <div>
            <h2 className="text-3xl sm:text-3xl lg:text-5xl font-bold text-gray-800 mb-4 mt-4 sm:mb-6">
              <span>Học tập hiệu quả với</span>
              <span className="block bg-gradient-to-r from-blue-500 to-blue-600 bg-clip-text text-transparent leading-relaxed">
                hệ thống quản lý học tập
              </span>
            </h2>
            <p className="text-md md:text-xl text-gray-600 mx-auto font-normal mb-6 sm:mb-8 leading-relaxed">
              <PERSON><PERSON> thống quản lý học tập trự<PERSON> tuyến giúp theo dõi tiến độ học tập,
              kết quả kiểm tra và tương tác với giáo viên mọi lúc mọi nơi.
            </p>

            <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-8">
              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Smartphone className="text-blue-600" size={18} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">
                    Theo dõi tiến độ
                  </h4>
                  <p className="text-gray-600 text-sm sm:text-base">
                    Xem tiến độ học tập qua 36 buổi học và kết quả các bài kiểm
                    tra
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Download className="text-blue-600" size={18} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">
                    Quản lý khóa học
                  </h4>
                  <p className="text-gray-600 text-sm sm:text-base">
                    Đăng ký, theo dõi và quản lý thông tin các khóa học đang
                    tham gia
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Play className="text-teal-600" size={18} />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800 mb-1 text-sm sm:text-base">
                    Tương tác trực tuyến
                  </h4>
                  <p className="text-gray-600 text-sm sm:text-base">
                    Liên hệ với giáo viên và nhận thông báo quan trọng
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <Button size="lg" onClick={onOpenRegistration}>
                Đăng ký học thử
                <svg
                  className="w-5 h-5 ml-1"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-[#0070F4] text-[#0070F4] font-bold flex items-center justify-center transition-colors duration-150 hover:border-[#0059c9] hover:bg-blue-50"
              >
                Tìm hiểu thêm
              </Button>
            </div>
          </div>

          <div className="relative order-first lg:order-last">
            <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl p-4 sm:p-6 lg:p-8 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <div className="relative z-10">
                <div className="bg-white rounded-2xl p-4 sm:p-6 shadow-2xl">
                  <div className="flex items-center justify-between mb-3 sm:mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-red-500 rounded-full"></div>
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span className="text-xs sm:text-sm text-gray-500">
                      Portal Học viên
                    </span>
                  </div>

                  <div className="space-y-3 sm:space-y-4">
                    <div className="bg-blue-50 rounded-xl p-3 sm:p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold text-gray-800 text-sm sm:text-base">
                          Khóa học hiện tại
                        </span>
                        <span className="text-lg sm:text-2xl">📚</span>
                      </div>
                      <div className="text-base sm:text-lg font-bold text-blue-600">
                        Toán học nâng cao
                      </div>
                      <div className="text-xs sm:text-sm text-gray-600">
                        Buổi 24/36
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3 sm:gap-4">
                      <div className="bg-blue-50 rounded-xl p-3 sm:p-4 text-center">
                        <div className="text-lg sm:text-2xl mb-1">📊</div>
                        <div className="text-xs sm:text-sm text-gray-600">
                          Điểm TB
                        </div>
                        <div className="font-bold text-blue-600 text-sm sm:text-base">
                          8.5/10
                        </div>
                      </div>
                      <div className="bg-teal-50 rounded-xl p-3 sm:p-4 text-center">
                        <div className="text-lg sm:text-2xl mb-1">🎯</div>
                        <div className="text-xs sm:text-sm text-gray-600">
                          Hoàn thành
                        </div>
                        <div className="font-bold text-teal-600 text-sm sm:text-base">
                          67%
                        </div>
                      </div>
                    </div>

                    <div className="bg-yellow-50 rounded-xl p-3 sm:p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-semibold text-gray-800 text-sm sm:text-base">
                          Bài kiểm tra tiếp theo
                        </span>
                        <span className="text-yellow-600 font-bold text-sm sm:text-base">
                          Buổi 27
                        </span>
                      </div>
                      <div className="text-xs sm:text-sm text-gray-600">
                        Kiểm tra giữa kỳ lần 3
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-16 h-16 sm:w-24 sm:h-24 bg-white/10 rounded-full blur-xl"></div>
              <div className="absolute -bottom-4 -left-4 w-20 h-20 sm:w-32 sm:h-32 bg-white/10 rounded-full blur-xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
