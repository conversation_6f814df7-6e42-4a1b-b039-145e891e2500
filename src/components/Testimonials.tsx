import {
  Star,
  TrendingUp,
  Users,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useEffect, useState } from "react";
import { getTestimonials, Testimonial } from "@/api/service/testimonials";
import { LoadingSpinner } from "@/components/LoadingSpinner";

const stats = [
  {
    icon: <Star className="w-7 h-7 text-white" />,
    iconBg: "bg-gradient-to-br from-[#7c3aed] to-[#a78bfa]",
    number: "2,500+",
    label: "Đánh giá 5 sao",
    text: "text-[#7c3aed]",
  },
  {
    icon: <TrendingUp className="w-7 h-7 text-white" />,
    iconBg: "bg-gradient-to-br from-[#a21caf] to-[#f472b6]",
    number: "4.9/5",
    label: "Điểm trung bình",
    text: "text-[#a21caf]",
  },
  {
    icon: <Users className="w-7 h-7 text-white" />,
    iconBg: "bg-gradient-to-br from-[#f87171] to-[#fb7185]",
    number: "95%",
    label: "Sẽ giới thiệu bạn bè",
    text: "text-[#f87171]",
  },
];

export default function TestimonialsSection({
  onOpenRegistration,
}: {
  onOpenRegistration?: () => void;
}) {
  const [currentTestimonialsPage, setCurrentTestimonialsPage] = useState(1);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([
    {
      avatar: "https://randomuser.me/api/portraits/women/68.jpg",
      name: "Nguyễn Thị Mai",
      role: "Sinh viên Đại học",
      quote:
        "Đức Hưng đã giúp tôi cải thiện kỹ năng nói tiếng Anh một cách đáng kinh ngạc. Phương pháp học tập thực tế và đội ngũ giáo viên nhiệt tình đã giúp tôi tự tin hơn rất nhiều.",
      badge: { label: "IELTS 7.5", color: "bg-[#2563eb]" },
    },
    {
      avatar: "https://randomuser.me/api/portraits/men/32.jpg",
      name: "Trần Văn Minh",
      role: "Nhân viên văn phòng",
      quote:
        "Lịch học linh hoạt và phương pháp giảng dạy hiệu quả của Đức Hưng đã giúp tôi cải thiện tiếng Anh trong công việc. Tôi đã được thăng chức sau khi hoàn thành khóa học Business English.",
      badge: { label: "Business English", color: "bg-[#22c55e]" },
    },
    {
      avatar: "https://randomuser.me/api/portraits/women/44.jpg",
      name: "Lê Thị Hương",
      role: "Giáo viên",
      quote:
        "Tôi đã học tại nhiều trung tâm khác nhau nhưng Đức Hưng thực sự khác biệt. Phương pháp học tập cá nhân hóa và môi trường học tập thân thiện đã giúp tôi đạt được mục tiêu TOEIC.",
      badge: { label: "TOEIC 950", color: "bg-[#a21caf]" },
    },
  ]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    setError(null);
    getTestimonials()
      .then((data) => {
        if (mounted) setTestimonials(data);
      })
      .catch(() => {
        setError("Không thể tải cảm nhận học viên. Hiển thị dữ liệu mẫu.");
      })
      .finally(() => setLoading(false));
    return () => {
      mounted = false;
    };
  }, []);

  // Testimonials pagination constants
  const TESTIMONIALS_PER_PAGE = 3;
  const totalTestimonials = testimonials.length;
  const totalTestimonialsPages = Math.ceil(
    totalTestimonials / TESTIMONIALS_PER_PAGE
  );

  // Testimonials pagination functions
  const scrollToTestimonialsSection = () => {
    const testimonialsSection = document.getElementById("testimonials-section");
    if (testimonialsSection) {
      const elementRect = testimonialsSection.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;
      const offset = 450;
      const scrollToPosition = absoluteElementTop + offset;

      window.scrollTo({
        top: scrollToPosition,
        behavior: "smooth",
      });
    }
  };

  const nextTestimonialsPage = () => {
    if (currentTestimonialsPage < totalTestimonialsPages) {
      setCurrentTestimonialsPage(currentTestimonialsPage + 1);
      setTimeout(() => scrollToTestimonialsSection(), 100);
    }
  };

  const prevTestimonialsPage = () => {
    if (currentTestimonialsPage > 1) {
      setCurrentTestimonialsPage(currentTestimonialsPage - 1);
      setTimeout(() => scrollToTestimonialsSection(), 100);
    }
  };

  const goToTestimonialsPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalTestimonialsPages) {
      setCurrentTestimonialsPage(pageNumber);
      setTimeout(() => scrollToTestimonialsSection(), 100);
    }
  };

  const getCurrentPageTestimonials = () => {
    const startIndex = (currentTestimonialsPage - 1) * TESTIMONIALS_PER_PAGE;
    const endIndex = startIndex + TESTIMONIALS_PER_PAGE;
    return testimonials.slice(startIndex, endIndex);
  };

  const currentTestimonials = getCurrentPageTestimonials();

  return (
    <section
      id="testimonials-section"
      className="w-full bg-gray-50 py-16 sm:py-16"
    >
      <div className="max-w-6xl mx-auto px-4">
        {/* Section Title */}
        <div className="text-center mb-10">
          {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-700 mb-6 shadow-sm">
            <span className="w-2 h-2 bg-primary rounded-full"></span>
            Cảm nhận học viên
          </span> */}
          <h2 className="text-4xl md:text-5xl font-bold mb-2 leading-relaxed">
            Học viên nói gì về <span className="text-[#0F4FAF]">Đức Hưng?</span>
          </h2>
          <div className="text-gray-500 text-base md:text-lg">
            Hàng nghìn học viên đã thành công với phương pháp học của chúng tôi
          </div>
        </div>
        {loading ? (
          <div>
            <LoadingSpinner isVisible={true} message="Đang tải..." />;
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : null}
        {/* Statistics */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 mb-16">
          {stats.map((stat, i) => (
            <div
              key={i}
              className="bg-white rounded-xl shadow-sm p-6 flex flex-col items-center text-center"
            >
              <div
                className={`w-16 h-16 flex items-center justify-center rounded-full mb-4 ${stat.iconBg}`}
              >
                {stat.icon}
              </div>
              <div className={`text-3xl font-bold mb-1 ${stat.text}`}>
                {stat.number}
              </div>
              <div className="text-gray-600 text-base font-medium">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="relative mb-16 flex flex-col gap-12">
          {/* Background circles */}
          <div className="absolute -top-8 -left-8 w-40 h-40 bg-[#7c3aed]/10 rounded-full z-0"></div>
          <div className="absolute top-1/2 right-0 w-32 h-32 bg-[#f472b6]/10 rounded-full z-0"></div>
          <div className="absolute bottom-0 left-1/2 w-28 h-28 bg-[#22c55e]/10 rounded-full z-0"></div>
          <div className="relative z-10 flex flex-col gap-12">
            {currentTestimonials.map((t, i) => (
              <div
                key={i}
                className="bg-white rounded-3xl shadow-lg px-8 py-8 flex flex-col md:flex-row items-start md:items-center gap-6 md:gap-8 max-w-5xl mx-auto min-h-[220px]"
              >
                <img
                  src={t.avatar}
                  alt={t.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-white shadow-lg flex-shrink-0"
                />
                <div className="flex-1 min-w-0">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                    <div>
                      <div className="font-bold text-lg text-gray-900 truncate">
                        {t.name}
                      </div>
                      <div className="text-gray-500 text-base font-medium truncate">
                        {t.role}
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-2 md:mt-0">
                      {[...Array(5)].map((_, idx) => (
                        <Star
                          key={idx}
                          className="w-5 h-5 text-yellow-400 fill-yellow-400"
                        />
                      ))}
                    </div>
                  </div>
                  <div className="font-regular text-gray-700 text-lg leading-relaxed mt-4 mb-4">
                    “{t.quote}”
                  </div>
                  <span
                    className={`inline-flex items-center gap-1 px-3 py-0.5 rounded-full font-semibold text-white text-xs shadow border border-white mt-2 whitespace-nowrap ${t.badge.color}`}
                  >
                    <svg width="16" height="24" fill="none" viewBox="0 0 20 20">
                      <circle
                        cx="10"
                        cy="10"
                        r="10"
                        fill="white"
                        opacity=".2"
                      />
                      <path
                        d="M10 3.5l1.09 3.36h3.53l-2.86 2.08 1.09 3.36L10 10.22l-2.86 2.08 1.09-3.36-2.86-2.08h3.53L10 3.5z"
                        fill="white"
                      />
                    </svg>
                    {t.badge.label}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials Pagination Controls */}
        {totalTestimonialsPages > 1 && (
          <div className="mt-16 flex flex-col items-center space-y-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={prevTestimonialsPage}
                disabled={currentTestimonialsPage === 1}
                className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                  currentTestimonialsPage === 1
                    ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                }`}
                aria-label="Previous page"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
              </button>

              <div className="flex items-center space-x-4">
                {Array.from({ length: totalTestimonialsPages }, (_, index) => {
                  const pageNumber = index + 1;
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => goToTestimonialsPage(pageNumber)}
                      className={`w-10 h-10 rounded-lg border transition-all duration-300 ${
                        pageNumber === currentTestimonialsPage
                          ? "bg-[#0070F4] text-white border-[#0070F4] shadow-md scale-110"
                          : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400"
                      }`}
                      aria-label={`Go to page ${pageNumber}`}
                      aria-current={
                        pageNumber === currentTestimonialsPage
                          ? "page"
                          : undefined
                      }
                    >
                      {pageNumber}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={nextTestimonialsPage}
                disabled={currentTestimonialsPage === totalTestimonialsPages}
                className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                  currentTestimonialsPage === totalTestimonialsPages
                    ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                }`}
                aria-label="Next page"
              >
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
