import {
  MapPin,
  Phone,
  Mail,
  Clock,
  ChevronLeft,
  ChevronRight,
  Map,
} from "lucide-react";
import { useEffect, useState } from "react";
import { getCampuses, Campus } from "@/api/service/campus";

// Campus data
const campusData = {
  title: "<PERSON><PERSON> thống cơ sở",
  subtitle:
    "Lựa chọn địa điểm thuận tiện nhất cho bạn trong hệ thống trung tâm của chúng tôi",
  campuses: [
    {
      id: 1,
      name: "Cơ sở Cửa Bắc",
      address: "Số 2 Cửa Bắc, Ba Đình, Hà Nội",
      phone: "024.3636.5555",
      email: "<EMAIL>",
      hours: "Thứ 2 - Ch<PERSON> nhật: 8:00 - 20:00",
      mapLink:
        "https://www.google.com/maps/search/?api=1&query=Số+2+Cửa+Bắc,+<PERSON>+Đ<PERSON>,+<PERSON><PERSON>+Nộ<PERSON>",
    },
    {
      id: 2,
      name: "<PERSON><PERSON> sở <PERSON>ý <PERSON>",
      address: "Số 45 Lý <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>",
      phone: "024.3737.6666",
      email: "<EMAIL>",
      hours: "Thứ 2 - Chủ nhật: 8:00 - 20:00",
      mapLink:
        "https://www.google.com/maps/search/?api=1&query=Số+45+Lý+Thường+Kiệt,+Hoàn+Kiếm,+Hà+Nội",
    },
  ],
};

export function CampusLocationsSection() {
  const [currentCentersPage, setCurrentCentersPage] = useState(1);
  const [campuses, setCampuses] = useState<Campus[]>(campusData.campuses);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    setError(null);
    getCampuses()
      .then((data) => {
        if (mounted) setCampuses(data);
      })
      .catch(() => {
        setError("Không thể tải danh sách cơ sở. Hiển thị dữ liệu mẫu.");
        setCampuses(campusData.campuses);
      })
      .finally(() => setLoading(false));
    return () => {
      mounted = false;
    };
  }, []);

  // Centers pagination constants
  const CENTERS_PER_PAGE = 2;
  const totalCenters = campuses.length;
  const totalCentersPages = Math.ceil(totalCenters / CENTERS_PER_PAGE);

  // Centers pagination functions
  const scrollToCentersSection = () => {
    const centersSection = document.getElementById("centers-section");
    if (centersSection) {
      const elementRect = centersSection.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;
      const offset = 100;
      const scrollToPosition = absoluteElementTop - offset;

      window.scrollTo({
        top: scrollToPosition,
        behavior: "smooth",
      });
    }
  };

  const nextCentersPage = () => {
    if (currentCentersPage < totalCentersPages) {
      setCurrentCentersPage(currentCentersPage + 1);
      setTimeout(() => scrollToCentersSection(), 100);
    }
  };

  const prevCentersPage = () => {
    if (currentCentersPage > 1) {
      setCurrentCentersPage(currentCentersPage - 1);
      setTimeout(() => scrollToCentersSection(), 100);
    }
  };

  const goToCentersPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalCentersPages) {
      setCurrentCentersPage(pageNumber);
      setTimeout(() => scrollToCentersSection(), 100);
    }
  };

  const getCurrentPageCenters = () => {
    const startIndex = (currentCentersPage - 1) * CENTERS_PER_PAGE;
    const endIndex = startIndex + CENTERS_PER_PAGE;
    return campuses.slice(startIndex, endIndex);
  };

  const currentCenters = getCurrentPageCenters();

  return (
    <section
      id="centers-section"
      className="relative py-32 overflow-hidden bg-gradient-to-b from-gray-50 via-gray-50 to-white"
    >
      {/* Decorative background blobs */}
      <div
        className="absolute -top-32 -left-32 w-[480px] h-[480px] bg-blue-500/20 rounded-full blur-3xl"
        aria-hidden="true"
      />
      <div
        className="absolute -bottom-40 -right-40 w-[520px] h-[520px] bg-blue-500/20 rounded-full blur-3xl"
        aria-hidden="true"
      />

      <div className="relative max-w-7xl mx-auto px-4">
        {/* Heading */}
        <div className="text-center mb-16">
          {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-700 mb-6 shadow-sm">
            <span className="w-2 h-2 bg-primary rounded-full"></span>
            Mạng lưới trung tâm
          </span> */}
          <h2 className="text-4xl md:text-5xl font-extrabold mb-4">
            Hệ thống <span className="text-[#0F4FAF]">cơ sở</span>
          </h2>
          <p className="text-gray-600 text-lg mx-auto">{campusData.subtitle}</p>
        </div>

        {loading ? (
          <div className="text-center py-12 text-lg text-gray-500">
            Đang tải danh sách cơ sở...
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : null}

        {/* Campuses grid */}
        <div className="grid gap-10 md:grid-cols-2">
          {currentCenters.map((campus) => (
            <article
              key={campus.id}
              className="flex flex-col rounded-3xl bg-white border border-transparent shadow-lg hover:shadow-xl transition-all p-10 hover:-translate-y-1 group"
              style={{
                backgroundImage:
                  "linear-gradient(#fff, #fff), radial-gradient(circle at top left, #60a5fa 0%, #3b82f6 100%)",
                backgroundOrigin: "padding-box, border-box",
                backgroundClip: "padding-box, border-box",
              }}
            >
              {/* Name */}
              <header className="flex items-center gap-4 mb-8">
                <div className="w-12 h-12 rounded-full bg-[#0F4FAF]/10 flex items-center justify-center group-hover:bg-[#0F4FAF] transition-colors">
                  <Map className="w-6 h-6 text-[#0F4FAF] group-hover:text-white transition-colors" />
                </div>
                <h3 className="text-2xl font-semibold leading-snug text-gray-900 group-hover:text-[#0F4FAF] transition-colors">
                  {campus.name}
                </h3>
              </header>

              {/* Info list */}
              <ul className="space-y-4 text-gray-700 flex-1">
                <li className="flex gap-3">
                  <MapPin className="w-5 h-5 text-[#0F4FAF] flex-shrink-0" />
                  <span>{campus.address}</span>
                </li>
                <li className="flex gap-3">
                  <Phone className="w-5 h-5 text-[#0F4FAF] flex-shrink-0" />
                  <a href={`tel:${campus.phone}`} className="hover:underline">
                    {campus.phone}
                  </a>
                </li>
                <li className="flex gap-3">
                  <Mail className="w-5 h-5 text-[#0F4FAF] flex-shrink-0" />
                  <a
                    href={`mailto:${campus.email}`}
                    className="hover:underline break-all"
                  >
                    {campus.email}
                  </a>
                </li>
                <li className="flex gap-3">
                  <Clock className="w-5 h-5 text-[#0F4FAF] flex-shrink-0" />
                  <span>{campus.hours}</span>
                </li>
              </ul>

              {/* Map link */}
              <a
                href={campus.mapLink}
                target="_blank"
                rel="noopener noreferrer"
                className="mt-8 inline-flex items-center text-sm font-semibold text-[#0F4FAF] hover:underline"
              >
                Xem bản đồ
                <svg
                  className="w-4 h-4 ml-1"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </a>
            </article>
          ))}
        </div>

        {/* Centers Pagination Controls */}
        {totalCentersPages > 1 && (
          <div className="mt-16 flex flex-col items-center space-y-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={prevCentersPage}
                disabled={currentCentersPage === 1}
                className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                  currentCentersPage === 1
                    ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                }`}
                aria-label="Previous page"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
              </button>

              <div className="flex items-center space-x-4">
                {Array.from({ length: totalCentersPages }, (_, index) => {
                  const pageNumber = index + 1;
                  return (
                    <button
                      key={pageNumber}
                      onClick={() => goToCentersPage(pageNumber)}
                      className={`w-10 h-10 rounded-lg border transition-all duration-300 ${
                        pageNumber === currentCentersPage
                          ? "bg-[#0070F4] text-white border-[#0070F4] shadow-md scale-110"
                          : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400"
                      }`}
                      aria-label={`Go to page ${pageNumber}`}
                      aria-current={
                        pageNumber === currentCentersPage ? "page" : undefined
                      }
                    >
                      {pageNumber}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={nextCentersPage}
                disabled={currentCentersPage === totalCentersPages}
                className={`flex items-center px-4 py-3 rounded-lg border transition-all duration-300 ${
                  currentCentersPage === totalCentersPages
                    ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-[#0070F4] hover:text-white hover:border-[#0070F4] hover:shadow-md"
                }`}
                aria-label="Next page"
              >
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
