import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Check } from "lucide-react";

const programData = [
  {
    id: "training",
    title: "Chương Trình <PERSON>",
    features: [
      {
        title: "Chuẩn Quốc Gia",
        desc: "<PERSON>ù hợp với Chương trình Giáo dục Phổ thông 2018 và hướng dẫn của Bộ/Sở Giáo dục",
      },
      {
        title: "Phân Hóa Cá Nhân",
        desc: "Giảng dạy phân hóa phù hợp với năng lự<PERSON>, trình độ học tập và nhu cầu cá nhân",
      },
      {
        title: "<PERSON>ý Thuyết + Thực Hành",
        desc: "Kết hợp lý thuyết (c<PERSON> bản + nâng cao) và ứng dụng thực hành",
      },
      {
        title: "<PERSON><PERSON><PERSON>",
        desc: "Hỗ trợ chuẩn bị cho các kỳ thi chuẩn hóa",
      },
    ],
  },
  {
    id: "objective",
    title: "<PERSON><PERSON><PERSON> Tiêu <PERSON>ơng Trình",
    features: [
      {
        title: "Phát triển năng lực toàn diện",
        desc: "Thúc đẩy sự phát triển toàn diện về cá nhân và học thuật của học viên",
      },
      {
        title: "Thành công trong thi cử",
        desc: "Giúp học sinh thành công trong các kỳ thi giữa kỳ, cuối kỳ, chuyển cấp và tốt nghiệp",
      },
      {
        title: "Chuẩn bị du học",
        desc: "Trang bị kỹ năng và sự chuẩn bị học thuật cho việc du học",
      },
      {
        title: "Nâng cao chuyên môn",
        desc: "Cung cấp các kỹ năng chuyên môn phù hợp với tiêu chuẩn cơ quan giáo dục",
      },
    ],
  },
  {
    id: "facility",
    title: "Cơ Sở Vật Chất",
    features: [
      {
        title: "Phòng học hiện đại",
        desc: "Đầy đủ trang thiết bị, tài nguyên chương trình học và tài liệu học tập",
      },
      {
        title: "Công nghệ hiện đại",
        desc: "Máy chiếu, loa, micro, bảng thông minh, bàn ghế hiện đại",
      },
      {
        title: "Thư viện số",
        desc: "Internet tốc độ cao cho nghiên cứu và tài nguyên cập nhật",
      },
    ],
  },
  {
    id: "certificate",
    title: "Chính Sách Chứng Chỉ & Đánh Giá",
    features: [
      {
        title: "Yêu cầu chứng chỉ",
        desc: "Kiểm tra giữa kỳ và cuối kỳ là bắt buộc để được cấp chứng chỉ hoàn thành",
      },
      {
        title: "Thời gian cấp chứng chỉ",
        desc: "Chứng chỉ hoàn thành được cấp trong vòng 7 ngày làm việc sau khi kết thúc khóa học",
      },
      {
        title: "Chứng chỉ quốc tế",
        desc: "Lựa chọn chứng chỉ quốc tế (trong kế hoạch hợp tác tương lai)",
      },
    ],
  },
] as const;

export function EducationProgramSection() {
  return (
    <section id="education-program" className="py-24 bg-white">
      <div className="max-w-5xl mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center mb-10">Chương trình đào tạo</h2>
        <Accordion type="multiple" className="border rounded-xl shadow-sm bg-white">
          {programData.map((section) => (
            <AccordionItem key={section.id} value={section.id} className="border-b last:border-b-0 px-4">
              <AccordionTrigger className="py-4 text-lg font-semibold text-gray-800 hover:bg-gray-50">
                {section.title}
              </AccordionTrigger>
              <AccordionContent className="py-4 space-y-4">
                {section.features.map((f, idx) => (
                  <div key={idx} className="space-y-1">
                    <div className="flex items-start gap-3">
                      <Check className="w-4 h-4 text-[#0070F4] mt-1" />
                      {f.title}
                    </div>
                    <p className="text-gray-600 text-sm md:text-base pl-7">{f.desc}</p>
                  </div>
                ))}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
} 