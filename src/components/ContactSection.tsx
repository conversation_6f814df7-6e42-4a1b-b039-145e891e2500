import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Check } from "lucide-react";

const contactSectionData = {
  left: {
    title: "Đăng ký ngay hôm nay",
    subtitle:
      "Để lại thông tin và chuyên viên của chúng tôi sẽ liên hệ, tư vấn lộ trình học phù hợp nhất cho bạn.",
    benefits: [
      "<PERSON><PERSON> nhân hóa theo mục tiêu",
      "<PERSON><PERSON><PERSON> học linh hoạt",
      "Ưu đãi học phí đặc biệt",
    ],
  },
  form: {
    fields: [
      {
        name: "fullName",
        label: "Họ và tên",
        placeholder: "Nhập họ và tên của bạn",
        required: true,
      },
      {
        name: "phone",
        label: "<PERSON><PERSON> điện thoại",
        placeholder: "<PERSON><PERSON>ậ<PERSON> số điện thoại",
        required: true,
      },
      {
        name: "campus",
        label: "Cơ sở quan tâm",
        type: "select",
        placeholder: "Chọn cơ sở",
        options: ["Cơ sở Cửa Bắc", "Cơ sở Lý Thường Kiệt"],
      },
      {
        name: "program",
        label: "Chương trình quan tâm",
        type: "select",
        placeholder: "Chọn chương trình",
        options: ["Tiếng Anh giao tiếp", "IELTS/TOEFL", "Kỹ năng mềm"],
      },
    ],
    submitText: "Đăng ký ngay",
  },
} as const;

export function ContactSection() {
  const initialState = contactSectionData.form.fields.reduce(
    (acc: Record<string, string>, f) => ({ ...acc, [f.name]: "" }),
    {}
  );
  const [formData, setFormData] = useState(initialState);

  const handleChange = (field: string, value: string) =>
    setFormData((prev) => ({ ...prev, [field]: value }));

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("submit", formData);
  };

  return (
    <section className="bg-white py-24" id="consultation">
      <div className="max-w-7xl mx-auto px-4 grid md:grid-cols-2 gap-12 items-center">
        {/* Left column */}
        <div>
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-[#0F4FAF]">
            Đăng ký ngay
            <span className="text-black"> hôm nay</span>
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-md">
            {contactSectionData.left.subtitle}
          </p>
          <ul className="space-y-3">
            {contactSectionData.left.benefits.map((b) => (
              <li key={b} className="flex items-center gap-3 text-gray-800">
                <span className="w-6 h-6 rounded-full bg-[#0F4FAF] flex items-center justify-center">
                  <Check className="w-4 h-4 text-white" />
                </span>
                {b}
              </li>
            ))}
          </ul>
        </div>

        {/* Right column */}
        <div>
          <form
            onSubmit={onSubmit}
            className="bg-white rounded-2xl border border-gray-200 shadow-md p-8 space-y-6"
          >
            {contactSectionData.form.fields.map((f) => {
              const field: any = f;
              if (field.type === "select") {
                return (
                  <div key={field.name}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {field.label}
                      {field.required && " *"}
                    </label>
                    <Select
                      value={formData[field.name]}
                      onValueChange={(v) => handleChange(field.name, v)}
                    >
                      <SelectTrigger className="w-full min-h-[48px] border border-gray-300 rounded-lg focus:border-[#0F4FAF] focus:ring-1 focus:ring-[#0F4FAF]">
                        <SelectValue placeholder={field.placeholder} />
                      </SelectTrigger>
                      <SelectContent className="rounded-lg border">
                        {field.options.map((opt: string) => (
                          <SelectItem
                            key={opt}
                            value={opt}
                            className="text-base"
                          >
                            {opt}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }
              return (
                <div key={field.name}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {field.label}
                    {field.required && " *"}
                  </label>
                  <Input
                    placeholder={field.placeholder}
                    required={field.required}
                    value={formData[field.name]}
                    onChange={(e) => handleChange(field.name, e.target.value)}
                    className="w-full min-h-[48px] border border-gray-300 rounded-lg focus:border-[#0F4FAF] focus:ring-1 focus:ring-[#0F4FAF]"
                  />
                </div>
              );
            })}
            <Button
              type="submit"
              className="w-full min-h-[48px] bg-[#0F4FAF] hover:bg-[#0059c9] text-white font-bold flex items-center justify-center gap-3 transition-colors duration-150 shadow-lg"
            >
              {contactSectionData.form.submitText}
            </Button>
          </form>
        </div>
      </div>
    </section>
  );
}
