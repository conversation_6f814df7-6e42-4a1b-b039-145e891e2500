import { useState } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { CheckCircle, Phone, MailCheck } from "lucide-react";

const contactFormData = {
  title: "Nhận tư vấn miễn phí",
  subtitle:
    "Để lại thông tin và chuyên viên của chúng tôi sẽ liên hệ, tư vấn lộ trình học phù hợp nhất cho bạn.",
  benefits: [
    "<PERSON><PERSON> nhân hóa theo mục tiêu",
    "<PERSON><PERSON><PERSON> học linh hoạt",
    "Ưu đãi học phí đặc biệt",
  ],
  form: {
    fields: [
      {
        name: "fullName",
        label: "Họ và tên",
        placeholder: "<PERSON>hậ<PERSON> họ và tên của bạn",
        required: true,
      },
      {
        name: "phone",
        label: "<PERSON><PERSON> điện thoại",
        placeholder: "<PERSON><PERSON>ậ<PERSON> số điện thoại",
        required: true,
      },
      {
        name: "campus",
        label: "Cơ sở quan tâm",
        type: "select",
        placeholder: "Chọn cơ sở",
        required: false,
        options: ["Cơ sở Cửa Bắc", "Cơ sở Lý Thường Kiệt"],
      },
      {
        name: "program",
        label: "Chương trình quan tâm",
        type: "select",
        placeholder: "Chọn chương trình",
        required: false,
        options: [
          "Toán nâng cao",
          "Luyện thi THPT Quốc gia",
          "Ôn thi chuyển cấp",
          "Ngoại ngữ",
        ],
      },
    ],
    submitText: "Đăng ký ngay",
  },
} as const;

export function ContactFormSection() {
  const initialState = contactFormData.form.fields.reduce(
    (acc: Record<string, string>, f) => {
      acc[f.name] = "";
      return acc;
    },
    {}
  );
  const [formData, setFormData] = useState(initialState);

  const handleChange = (field: string, value: string) =>
    setFormData((prev) => ({ ...prev, [field]: value }));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: validation & submit logic
    console.log("contact form submitted", formData);
  };

  return (
    <section className="py-32 bg-white" id="contact">
      <div className="max-w-7xl mx-auto grid gap-14 md:grid-cols-5 px-4">
        {/* Intro */}
        <div className="md:col-span-2 flex flex-col justify-center order-2 md:order-1">
          <h2 className="text-3xl md:text-4xl font-extrabold mb-6">
            {contactFormData.title.split(" ")[0]}{" "}
            <span className="text-gradient">
              {contactFormData.title.split(" ").slice(1).join(" ")}
            </span>
          </h2>
          <p className="text-gray-600 text-lg mb-8 max-w-md">
            {contactFormData.subtitle}
          </p>
          <ul className="space-y-3 text-gray-700">
            {contactFormData.benefits.map((b) => (
              <li key={b} className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{b}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Form */}
        <div className="md:col-span-3 bg-gray-50 rounded-xl border border-gray-200 p-8 md:p-10 shadow-sm order-1 md:order-2">
          <form onSubmit={handleSubmit} className="grid gap-6 md:grid-cols-2">
            {contactFormData.form.fields.map((field) => {
              if ((field as any).type === "select") {
                const f = field as any;
                return (
                  <div key={field.name} className="md:col-span-1">
                    <label className="block text-gray-700 font-medium mb-2 text-sm">
                      {field.label}
                      {field.required && " *"}
                    </label>
                    <Select
                      value={formData[field.name]}
                      onValueChange={(v) => handleChange(field.name, v)}
                    >
                      <SelectTrigger className="w-full min-h-[48px] text-base border-2 border-gray-200 focus:border-primary rounded-lg">
                        <SelectValue placeholder={field.placeholder} />
                      </SelectTrigger>
                      <SelectContent className="rounded-lg border-2">
                        {f.options?.map((opt: string) => (
                          <SelectItem
                            key={opt}
                            value={opt}
                            className="text-base"
                          >
                            {opt}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }
              return (
                <div key={field.name} className="md:col-span-1">
                  <label className="block text-gray-700 font-medium mb-2 text-sm">
                    {field.label}
                    {field.required && " *"}
                  </label>
                  <Input
                    placeholder={field.placeholder}
                    required={field.required}
                    value={formData[field.name]}
                    onChange={(e) => handleChange(field.name, e.target.value)}
                    className="w-full min-h-[48px] text-base border-2 border-gray-200 focus:border-primary rounded-lg"
                  />
                </div>
              );
            })}

            <div className="md:col-span-2 mt-2">
              <Button
                size="lg"
                type="submit"
                className="bg-[#0070F4] hover:bg-[#0059c9] text-white font-bold flex items-center justify-center gap-3 transition-colors duration-150 shadow-lg"
              >
                {contactFormData.form.submitText}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}
