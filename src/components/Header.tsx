import { <PERSON>u, X } from "lucide-react";
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoginModal } from "@/components/LoginModal";
import { RegistrationModal } from "@/components/RegistrationModal";
import { useNavigate, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { scrollToTop } from "@/hooks/useScrollToTop";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const navigate = useNavigate();
  const location = useLocation();

  const isActivePage = (path: string) => {
    const currentPath = location.pathname;

    // Exact match for most pages
    if (currentPath === path) {
      return true;
    }

    // Handle detail pages - highlight parent menu items when on child routes
    switch (path) {
      case "/teacher":
        // Highlight "Giáo viên" when on teacher detail pages (/teacher/{teacherId})
        return (
          currentPath.startsWith("/teacher/") && currentPath !== "/teacher"
        );
      case "/blog":
        // Highlight "Tin tức" when on blog detail pages (/blog/{blogId})
        return currentPath.startsWith("/blog/") && currentPath !== "/blog";
      case "/courses":
        // Highlight "Khóa học" when on course detail pages (/courses/{courseId})
        return (
          currentPath.startsWith("/courses/") && currentPath !== "/courses"
        );
      default:
        return false;
    }
  };

  const getNavItemClass = (path: string) => {
    return isActivePage(path)
      ? "nav-item text-[#0F4FAF] font-semibold"
      : "nav-item";
  };

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (session) {
      setIsLoggedIn(true);
      setUserData(session.user);
    }

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (session) {
        setIsLoggedIn(true);
        setUserData(session.user);
      } else {
        setIsLoggedIn(false);
        setUserData(null);
      }
    });

    return () => subscription.unsubscribe();
  };

  const handleLogout = async () => {
    await supabase.auth.signOut();
    navigate("/");
    scrollToTop("instant");
  };

  // Helper function to navigate and scroll to top
  const navigateAndScrollToTop = (path: string) => {
    navigate(path);
    // Small delay to ensure navigation completes before scrolling
    setTimeout(() => {
      scrollToTop("smooth");
    }, 100);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto container-padding">
        <div className="flex h-16 items-center justify-between">
          <div
            className="flex items-center space-x-2 cursor-pointer group"
            onClick={() => navigateAndScrollToTop("/")}
          >
            <div className="w-36 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform">
              <img
                src="/assets/Logo.svg"
                alt="Logo"
                className="w-full h-auto p-1"
              />
            </div>
            {/* <div className="flex flex-col">
              <span className="text-xl font-bold text-[#0070F4] tracking-tight">
                Đức Hưng
              </span>
              <span className="text-xs text-foreground -mt-1">
                Kiến tạo tương lai
              </span>
            </div> */}
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <button
              onClick={() => navigateAndScrollToTop("/")}
              className={getNavItemClass("/")}
            >
              Trang chủ
            </button>
            <button
              onClick={() => navigateAndScrollToTop("/courses")}
              className={getNavItemClass("/courses")}
            >
              Khóa học
            </button>
            <button
              onClick={() => navigateAndScrollToTop("/teacher")}
              className={getNavItemClass("/teacher")}
            >
              Giáo viên
            </button>
            <button
              onClick={() => navigateAndScrollToTop("/blog")}
              className={getNavItemClass("/blog")}
            >
              Tin tức
            </button>
            <button
              onClick={() => navigateAndScrollToTop("/about-us")}
              className={getNavItemClass("/about-us")}
            >
              Về chúng tôi
            </button>
          </nav>

          <div className="hidden md:flex items-center space-x-3">
            {isLoggedIn ? (
              <div className="flex items-center space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateAndScrollToTop("/dashboard")}
                  className="text-muted-foreground hover:text-foreground"
                >
                  Dashboard
                </Button>
                <Button variant="outline" size="sm" onClick={handleLogout}>
                  Đăng xuất
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                {/* <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLoginModal(true)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  Đăng nhập
                </Button> */}
                <Button
                  size="sm"
                  onClick={() => setShowRegistrationModal(true)}
                  className="btn-primary group bg-[#0F4FAF] hover:bg-[#0059c9] text-white shadow px-6"
                >
                  Đăng ký
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 hover:bg-accent rounded-lg transition-colors"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t">
            <nav className="flex flex-col space-y-1 py-4">
              <button
                className={`${getNavItemClass(
                  "/"
                )} py-3 px-2 rounded-lg hover:bg-accent transition-colors text-left`}
                onClick={() => {
                  navigateAndScrollToTop("/");
                  setIsMenuOpen(false);
                }}
              >
                Trang chủ
              </button>
              <button
                className={`${getNavItemClass(
                  "/courses"
                )} py-3 px-2 rounded-lg hover:bg-accent transition-colors text-left`}
                onClick={() => {
                  navigateAndScrollToTop("/courses");
                  setIsMenuOpen(false);
                }}
              >
                Khóa học
              </button>
              <button
                className={`${getNavItemClass(
                  "/teacher"
                )} py-3 px-2 rounded-lg hover:bg-accent transition-colors text-left`}
                onClick={() => {
                  navigateAndScrollToTop("/teacher");
                  setIsMenuOpen(false);
                }}
              >
                Giáo viên
              </button>
              <button
                className={`${getNavItemClass(
                  "/blog"
                )} py-3 px-2 rounded-lg hover:bg-accent transition-colors text-left`}
                onClick={() => {
                  navigateAndScrollToTop("/blog");
                  setIsMenuOpen(false);
                }}
              >
                Tin tức
              </button>
              <button
                className={`${getNavItemClass(
                  "/about-us"
                )} py-3 px-2 rounded-lg hover:bg-accent transition-colors text-left`}
                onClick={() => {
                  navigateAndScrollToTop("/about-us");
                  setIsMenuOpen(false);
                }}
              >
                Về chúng tôi
              </button>
              <div className="flex flex-col space-y-3 pt-4 border-t">
                {isLoggedIn ? (
                  <>
                    <Button
                      variant="ghost"
                      onClick={() => navigateAndScrollToTop("/dashboard")}
                      className="justify-start"
                    >
                      Dashboard
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleLogout}
                      className="justify-start"
                    >
                      Đăng xuất
                    </Button>
                  </>
                ) : (
                  <>
                    {/* <Button
                      variant="ghost"
                      onClick={() => setShowLoginModal(true)}
                      className="justify-start"
                    >
                      Đăng nhập
                    </Button> */}
                    <Button
                      type="submit"
                      size="lg"
                      onClick={() => setShowRegistrationModal(true)}
                    >
                      Đăng ký
                    </Button>
                  </>
                )}
              </div>
            </nav>
          </div>
        )}
      </div>

      <LoginModal open={showLoginModal} onOpenChange={setShowLoginModal} />
      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </header>
  );
}
