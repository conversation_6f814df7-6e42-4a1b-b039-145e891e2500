import { Play, Star, Users, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { RegistrationModal } from "@/components/RegistrationModal";
import { useState } from "react";

export function Hero() {
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);

  const handleDemoClick = () => {
    window.open("", "_blank");
  };

  return (
    <section className="py-16 relative h-fit bg-gradient-to-b from-background to-muted/20">
      {/* Background decorations */}
      <div className="absolute h-full inset-0 overflow-hidden z-0">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-200 to-blue-300 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#38bdf8] to-[#0070F4] rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute top-20 left-1/4 w-32 h-32 bg-gradient-to-br from-yellow-200 to-orange-200 rounded-full opacity-30 blur-2xl"></div>
      </div>

      <div className="container mx-auto container-padding relative z-10">
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 items-center">
          <div className="space-y-8 animate-fade-in order-2 lg:order-1">
            <div className="space-y-6">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-[#0F4FAF]/10 text-[#0F4FAF] text-sm font-medium">
                <Star className="w-4 h-4 mr-2 fill-current" />
                Được tin tưởng bởi 10,000+ học viên
              </div>

              <h1 className="font-bold">
                <span className="block text-4xl md:text-4xl lg:text-6xl leading-[1.15] mb-2">
                  Giáo dục chất lượng
                </span>
                <span className="block text-[#0F4FAF] md:leading-[1.3] lg:leading-[1.3] mb-2 overflow-visible text-4xl md:text-4xl lg:text-6xl">
                  phát triển toàn diện
                </span>
                <span className="block text-foreground/80 text-4xl md:text-4xl lg:text-6xl leading-[1.15] mb-2">
                  kiến tạo tương lai
                </span>
              </h1>

              <p className="text-lg md:text-lg lg:text-xl text-muted-foreground font-normal leading-relaxed max-w-2xl">
                Chúng tôi mang đến lộ trình học tập cá nhân hóa, kết hợp lý
                thuyết và thực hành, được thiết kế riêng cho học sinh Việt Nam.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                onClick={() => setShowRegistrationModal(true)}
                className="bg-[#0F4FAF] group"
              >
                Đăng ký học thử
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                onClick={handleDemoClick}
                className="btn-secondary group"
              >
                <Play className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
                Xem giới thiệu
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-8 pt-4">
              <div className="flex items-center space-x-3">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-5 h-5 text-yellow-400 fill-current"
                    />
                  ))}
                </div>
                <span className="text-muted-foreground font-medium">
                  4.9/5 (1,500+ đánh giá)
                </span>
              </div>
              <div className="flex items-center space-x-3">
                <Users className="w-5 h-5 text-primary" />
                <span className="text-muted-foreground font-medium">
                  10,000+ học viên
                </span>
              </div>
            </div>
          </div>
          <div className="relative animate-scale-in order-1 lg:order-2">
            <div className="max-w-xs sm:max-w-md mx-auto">
              <img
                src="/assets/image-1.png"
                alt="Học sinh đang học tập"
                className="w-full h-auto p-2"
              />
            </div>
          </div>
        </div>
      </div>

      <RegistrationModal
        open={showRegistrationModal}
        onOpenChange={setShowRegistrationModal}
      />
    </section>
  );
}
