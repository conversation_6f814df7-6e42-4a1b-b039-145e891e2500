import { MapPin, Phone, Mail, Building, Award, Users, BookOpen, GraduationCap, Target, Lightbulb, CheckCircle } from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export function CompanyInfo() {
  return (
    <section className="py-20 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-full text-sm font-medium text-gray-600 mb-6">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            Khái quát khóa học
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            T<PERSON><PERSON> quan <span className="text-primary">chư<PERSON>ng trình</span>
          </h2>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Khám phá chương trình đào tạo hiện đại với phương pháp tiên tiến
          </p>
        </div>

        {/* Clean Accordion */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <Accordion type="single" collapsible className="w-full space-y-4">
              <AccordionItem value="curriculum" className="border-0">
                <AccordionTrigger className="rounded-lg bg-blue-50 p-4 text-gray-800 hover:bg-blue-100 transition-colors border-0 hover:no-underline">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <BookOpen className="text-blue-600" size={20} />
                    </div>
                    <span className="text-lg font-semibold">Chương Trình Đào Tạo</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 pb-2">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-blue-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="text-blue-600" size={16} />
                        <h4 className="font-semibold text-gray-800">Chuẩn Quốc Gia</h4>
                      </div>
                      <p className="text-gray-600 text-sm">Phù hợp với Chương trình Giáo dục Phổ thông 2018 và hướng dẫn của Bộ/Sở Giáo dục</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-blue-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="text-blue-600" size={16} />
                        <h4 className="font-semibold text-gray-800">Phân Hóa Cá Nhân</h4>
                      </div>
                      <p className="text-gray-600 text-sm">Giảng dạy phân hóa phù hợp với năng lực, trình độ học tập và nhu cầu cá nhân</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-blue-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="text-blue-600" size={16} />
                        <h4 className="font-semibold text-gray-800">Lý Thuyết + Thực Hành</h4>
                      </div>
                      <p className="text-gray-600 text-sm">Kết hợp lý thuyết (cơ bản + nâng cao) và ứng dụng thực hành</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-blue-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <CheckCircle className="text-blue-600" size={16} />
                        <h4 className="font-semibold text-gray-800">Chuẩn Bị Thi Cử</h4>
                      </div>
                      <p className="text-gray-600 text-sm">Hỗ trợ chuẩn bị cho các kỳ thi chuẩn hóa</p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="goals" className="border-0">
                <AccordionTrigger className="rounded-lg bg-green-50 p-4 text-gray-800 hover:bg-green-100 transition-colors border-0 hover:no-underline">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Target className="text-green-600" size={20} />
                    </div>
                    <span className="text-lg font-semibold">Mục Tiêu Chương Trình</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 pb-2">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="flex items-start space-x-3 p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-green-400">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Award className="text-green-600" size={20} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">Phát triển năng lực toàn diện</h4>
                        <p className="text-gray-600 text-sm">Thúc đẩy sự phát triển toàn diện về cá nhân và học thuật của học viên</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-green-400">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <BookOpen className="text-green-600" size={20} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">Thành công trong thi cử</h4>
                        <p className="text-gray-600 text-sm">Giúp học sinh thành công trong các kỳ thi giữa kỳ, cuối kỳ, chuyển cấp và tốt nghiệp</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-green-400">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Users className="text-green-600" size={20} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">Chuẩn bị du học</h4>
                        <p className="text-gray-600 text-sm">Trang bị kỹ năng và sự chuẩn bị học thuật cho việc du học</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-green-400">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Building className="text-green-600" size={20} />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-1">Nâng cao chuyên môn</h4>
                        <p className="text-gray-600 text-sm">Cung cấp các kỹ năng chuyên môn phù hợp với tiêu chuẩn cơ quan giáo dục</p>
                      </div>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="facilities" className="border-0">
                <AccordionTrigger className="rounded-lg bg-purple-50 p-4 text-gray-800 hover:bg-purple-100 transition-colors border-0 hover:no-underline">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Building className="text-purple-600" size={20} />
                    </div>
                    <span className="text-lg font-semibold">Cơ Sở Vật Chất</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 pb-2">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-purple-400">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <Building className="text-purple-600" size={20} />
                      </div>
                      <h4 className="font-semibold text-gray-800 mb-2">Phòng học hiện đại</h4>
                      <p className="text-gray-600 text-sm">Đầy đủ trang thiết bị, tài nguyên chương trình học và tài liệu học tập</p>
                    </div>
                    <div className="text-center p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-purple-400">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <Lightbulb className="text-purple-600" size={20} />
                      </div>
                      <h4 className="font-semibold text-gray-800 mb-2">Công nghệ hiện đại</h4>
                      <p className="text-gray-600 text-sm">Máy chiếu, loa, micro, bảng thông minh, bàn ghế hiện đại</p>
                    </div>
                    <div className="text-center p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-purple-400">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <BookOpen className="text-purple-600" size={20} />
                      </div>
                      <h4 className="font-semibold text-gray-800 mb-2">Thư viện số</h4>
                      <p className="text-gray-600 text-sm">Internet tốc độ cao cho nghiên cứu và tài nguyên cập nhật</p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="certificate" className="border-0">
                <AccordionTrigger className="rounded-lg bg-orange-50 p-4 text-gray-800 hover:bg-orange-100 transition-colors border-0 hover:no-underline">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Award className="text-orange-600" size={20} />
                    </div>
                    <span className="text-lg font-semibold">Chính Sách Chứng Chỉ & Đánh Giá</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4 pb-2">
                  <div className="space-y-4">
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-orange-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="p-1 bg-orange-100 rounded-lg">
                          <CheckCircle className="text-orange-600" size={16} />
                        </div>
                        <h4 className="font-semibold text-gray-800">Yêu cầu chứng chỉ</h4>
                      </div>
                      <p className="text-gray-600 text-sm ml-8">Kiểm tra giữa kỳ và cuối kỳ là bắt buộc để được cấp chứng chỉ hoàn thành</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-orange-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="p-1 bg-orange-100 rounded-lg">
                          <CheckCircle className="text-orange-600" size={16} />
                        </div>
                        <h4 className="font-semibold text-gray-800">Thời gian cấp chứng chỉ</h4>
                      </div>
                      <p className="text-gray-600 text-sm ml-8">Chứng chỉ hoàn thành được cấp trong vòng 7 ngày làm việc sau khi kết thúc khóa học</p>
                    </div>
                    <div className="p-4 bg-white rounded-lg border border-gray-200 border-l-4 border-l-orange-400">
                      <div className="flex items-center space-x-2 mb-2">
                        <div className="p-1 bg-orange-100 rounded-lg">
                          <CheckCircle className="text-orange-600" size={16} />
                        </div>
                        <h4 className="font-semibold text-gray-800">Chứng chỉ quốc tế</h4>
                      </div>
                      <p className="text-gray-600 text-sm ml-8">Lựa chọn chứng chỉ quốc tế (trong kế hoạch hợp tác tương lai)</p>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  );
}
