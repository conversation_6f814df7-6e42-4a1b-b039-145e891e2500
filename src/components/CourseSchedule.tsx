import {
  <PERSON><PERSON><PERSON>,
  Check<PERSON>ir<PERSON>,
  BookO<PERSON>,
  Clipboard<PERSON>heck,
  Play,
} from "lucide-react";
import { Button } from "@/components/ui/button";

export function CourseSchedule() {
  const scheduleItems = [
    {
      session: "Buổi 1",
      description: "Kiểm tra đầu vào, phân loại và định hướng",
      type: "start",
      icon: Play,
      color: "emerald",
    },
    {
      session: "Buổi 2-8",
      description: "<PERSON><PERSON><PERSON> tập theo chương trình và bài tập",
      type: "learning",
      icon: BookOpen,
      color: "blue",
    },
    {
      session: "Buổi 9",
      description: "Kiểm tra lần 1",
      type: "assessment",
      icon: ClipboardCheck,
      color: "orange",
    },
    {
      session: "Buổi 10-17",
      description: "Ch<PERSON><PERSON> bài, học chương trình và bài tập",
      type: "learning",
      icon: BookOpen,
      color: "blue",
    },
    {
      session: "Buổi 18",
      description: "Kiểm tra lần 2",
      type: "assessment",
      icon: Clip<PERSON><PERSON><PERSON><PERSON>,
      color: "orange",
    },
    {
      session: "<PERSON>uổi 19-26",
      description: "<PERSON><PERSON><PERSON> bài, họ<PERSON> chương tr<PERSON>nh và bài tập",
      type: "learning",
      icon: BookOpen,
      color: "blue",
    },
    {
      session: "Buổi 27",
      description: "Kiểm tra lần 3",
      type: "assessment",
      icon: ClipboardCheck,
      color: "orange",
    },
    {
      session: "Buổi 28-35",
      description: "Chữa bài, học chương trình và bài tập",
      type: "learning",
      icon: BookOpen,
      color: "blue",
    },
    {
      session: "Buổi 36",
      description: "Kiểm tra cuối khóa",
      type: "final",
      icon: CheckCircle,
      color: "purple",
    },
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      emerald: {
        bg: "bg-emerald-500",
        text: "text-emerald-700",
        border: "border-emerald-200",
        cardBg: "bg-emerald-50",
      },
      blue: {
        bg: "bg-blue-500",
        text: "text-blue-700",
        border: "border-blue-200",
        cardBg: "bg-blue-50",
      },
      orange: {
        bg: "bg-orange-500",
        text: "text-orange-700",
        border: "border-orange-200",
        cardBg: "bg-orange-50",
      },
      purple: {
        bg: "bg-purple-500",
        text: "text-purple-700",
        border: "border-purple-200",
        cardBg: "bg-purple-50",
      },
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mx-4 mb-12">
          {/* <div className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            Lộ trình học tập
          </div> */}

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Tiến trình khóa học <span className="text-primary">36 buổi</span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Lộ trình học tập được thiết kế khoa học, đảm bảo hiệu quả tối ưu cho
            từng giai đoạn
          </p>
        </div>

        {/* Timeline Container */}
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Timeline Spine */}
            <div className="absolute left-8 md:left-1/2 md:-ml-1 top-0 bottom-0 w-0.5 bg-gray-300"></div>

            {/* Timeline Items */}
            <div className="space-y-8">
              {scheduleItems.map((item, index) => {
                const colors = getColorClasses(item.color);
                const isEven = index % 2 === 0;
                const progress = ((index + 1) / scheduleItems.length) * 100;

                return (
                  <div
                    key={index}
                    className={`relative flex items-center ${
                      isEven ? "md:flex-row" : "md:flex-row-reverse"
                    }`}
                  >
                    {/* Timeline Node */}
                    <div
                      className={`absolute left-8 md:left-1/2 md:-ml-8 w-16 h-16 ${colors.bg} rounded-full flex items-center justify-center z-10 border-4 border-white`}
                    >
                      <item.icon className="w-7 h-7 text-white" />
                    </div>

                    {/* Content Card */}
                    <div
                      className={`ml-24 md:ml-0 ${
                        isEven ? "md:mr-12 md:ml-0" : "md:ml-12 md:mr-0"
                      } md:w-5/12`}
                    >
                      <div
                        className={`bg-white border-2 ${colors.border} rounded-2xl p-6 hover:shadow-lg transition-all duration-300`}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h3 className={`font-bold ${colors.text} text-xl`}>
                            {item.session}
                          </h3>
                          <div
                            className={`px-3 py-1 ${colors.cardBg} rounded-full`}
                          >
                            <span
                              className={`text-sm font-medium ${colors.text}`}
                            >
                              {Math.round(progress)}%
                            </span>
                          </div>
                        </div>

                        <p className="w-[250px] text-gray-700 text-base leading-relaxed mb-4">
                          {item.description}
                        </p>

                        {/* Progress Bar */}
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${colors.bg} transition-all duration-500`}
                            style={{ width: `${progress}%` }}
                          ></div>
                        </div>

                        {/* Session Type Badge */}
                        <div className="mt-3">
                          <span
                            className={`inline-block px-3 py-1 text-xs font-medium rounded-full ${colors.cardBg} ${colors.text}`}
                          >
                            {item.type === "start" && "Khởi đầu"}
                            {item.type === "learning" && "Học tập"}
                            {item.type === "assessment" && "Đánh giá"}
                            {item.type === "final" && "Hoàn thành"}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Phase Number (Desktop Only) */}
                    <div
                      className={`hidden md:block ${
                        isEven ? "md:ml-12" : "md:mr-12"
                      } md:w-5/12 ${isEven ? "text-left" : "text-right"}`}
                    >
                      <div
                        className={`inline-block px-4 py-2 bg-white border-2 ${colors.border} rounded-xl`}
                      >
                        <span
                          className={`text-sm font-semibold ${colors.text}`}
                        >
                          Giai đoạn {index + 1}/9
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 flex justify-center">
          <Button
            size="lg"
            className="bg-[#0070F4] hover:bg-[#0059c9] text-white flex items-center justify-center gap-3 transition-colors duration-150 shadow-lg"
          >
            Xem chi tiết
            <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </div>
    </section>
  );
}
