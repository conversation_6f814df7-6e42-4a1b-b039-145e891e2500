interface PageHeaderProps {
  badge?: string;
  title: string;
  subtitle?: string;
  highlightedWords?: string[];
}

export function PageHeader({ badge, title, subtitle, highlightedWords = [] }: PageHeaderProps) {
  const renderTitle = () => {
    if (highlightedWords.length === 0) {
      return <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">{title}</h1>;
    }

    const words = title.split(' ');
    return (
      <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
        {words.map((word, index) => {
          const isHighlighted = highlightedWords.some(hw => 
            word.toLowerCase().includes(hw.toLowerCase())
          );
          
          return (
            <span key={index}>
              {isHighlighted ? (
                <span className="text-[#0070F4]">{word}</span>
              ) : (
                word
              )}
              {index < words.length - 1 ? ' ' : ''}
            </span>
          );
        })}
      </h1>
    );
  };

  return (
    <section className="section-padding container-padding text-center">
      <div className="max-w-4xl mx-auto">
        {badge && (
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-50 border border-blue-200 mb-6">
            <div className="w-2 h-2 bg-[#0070F4] rounded-full mr-3"></div>
            <span className="text-[#0070F4] font-medium text-sm">{badge}</span>
          </div>
        )}
        
        {renderTitle()}
        
        {subtitle && (
          <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
            {subtitle}
          </p>
        )}
      </div>
    </section>
  );
}
