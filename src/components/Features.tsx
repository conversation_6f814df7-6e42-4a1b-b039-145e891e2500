import {
  Target,
  GraduationCap,
  Award,
  Laptop,
  Globe,
  MessageSquare,
} from "lucide-react";

export function Features() {
  const features = [
    {
      icon: Target,
      title: "<PERSON>ọ<PERSON> tập cá nhân hóa",
      subtitle: "<PERSON><PERSON> hợp với từng học viên",
      description:
        "<PERSON><PERSON> trình được thiết kế riêng, phù hợp với khả năng và mục tiêu của bạn",
      color: "bg-purple-500",
      gradientFrom: "from-purple-200",
      gradientTo: "to-purple-100",
      iconBg: "bg-purple-500",
      accentColor: "bg-purple-500",
    },
    {
      icon: GraduationCap,
      title: "Giáo viên xuất sắc",
      subtitle: "Kinh nghiệm chuyên sâu",
      description:
        "Đội ngũ giáo viên có trình độ cao, kinh nghiệm thực tế trong ngành",
      color: "bg-blue-500",
      gradientFrom: "from-blue-200",
      gradientTo: "to-blue-100",
      iconBg: "bg-blue-500",
      accentColor: "bg-blue-500",
    },
    {
      icon: Award,
      title: "Chứng chỉ nhanh chóng",
      subtitle: "Hoàn thành trong 7 ngày",
      description:
        "Nhận chứng chỉ được công nhận ngay sau khi hoàn thành khóa học",
      color: "bg-green-500",
      gradientFrom: "from-green-200",
      gradientTo: "to-green-100",
      iconBg: "bg-green-500",
      accentColor: "bg-green-500",
    },
    {
      icon: Laptop,
      title: "Linh hoạt học tập",
      subtitle: "Online & Offline",
      description:
        "Lựa chọn hình thức học phù hợp với lịch trình và sở thích cá nhân",
      color: "bg-orange-500",
      gradientFrom: "from-orange-200",
      gradientTo: "to-orange-100",
      iconBg: "bg-orange-500",
      accentColor: "bg-orange-500",
    },
    {
      icon: Globe,
      title: "Chuẩn bị du học",
      subtitle: "Tiêu chuẩn quốc tế",
      description:
        "Chương trình đào tạo theo tiêu chuẩn toàn cầu, sẵn sàng cho du học",
      color: "bg-indigo-500",
      gradientFrom: "from-indigo-200",
      gradientTo: "to-indigo-100",
      iconBg: "bg-indigo-500",
      accentColor: "bg-indigo-500",
    },
    {
      icon: MessageSquare,
      title: "Phản hồi tức thì",
      subtitle: "Cải thiện liên tục",
      description:
        "Nhận feedback chi tiết và hỗ trợ cải thiện kỹ năng liên tục",
      color: "bg-pink-500",
      gradientFrom: "from-pink-200",
      gradientTo: "to-pink-100",
      iconBg: "bg-pink-500",
      accentColor: "bg-pink-500",
    },
  ];

  return (
    <section className="py-20 lg:py-28 bg-gray-50 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full bg-white">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary/5 rounded-full"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-blue-500/5 rounded-full"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500/5 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 max-w-7xl relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          {/* <div className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
            <div className="w-2 h-2 bg-primary rounded-full"></div>
            Tại sao chọn Đức Hưng
          </div> */}

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 leading-relaxed">
            Phương pháp giáo dục
            <br />
            <span className="text-primary block lg:mt-2">
              hiện đại & hiệu quả
            </span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Lấy học sinh làm trung tâm, thực tế và tích hợp công nghệ tiên tiến
          </p>
        </div>

        {/* Features List */}
        <div className="space-y-20 max-w-[1100px] mx-auto">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`flex flex-col lg:flex-row items-center gap-12 lg:gap-16 ${
                index % 2 === 1 ? "lg:flex-row-reverse" : ""
              }`}
            >
              {/* Icon Section */}
              <div className="flex-shrink-0 relative">
                <div className="relative w-80 h-80">
                  {/* Outer gradient circle */}
                  <div
                    className={`absolute inset-0 rounded-full bg-gradient-to-br ${feature.gradientFrom} ${feature.gradientTo} opacity-30`}
                  ></div>

                  {/* Middle gradient circle */}
                  <div
                    className={`absolute inset-8 rounded-full bg-gradient-to-br ${feature.gradientFrom} ${feature.gradientTo} opacity-50`}
                  ></div>

                  {/* Inner gradient circle */}
                  <div
                    className={`absolute inset-16 rounded-full bg-gradient-to-br ${feature.gradientFrom} ${feature.gradientTo} opacity-70`}
                  ></div>

                  {/* Icon container */}
                  <div
                    className={`absolute inset-24 rounded-full ${feature.iconBg} flex items-center justify-center shadow-lg hover:scale-105 transition-transform duration-300`}
                  >
                    <feature.icon className="w-12 h-12 text-white" />
                  </div>

                  {/* Floating dots */}
                  <div
                    className={`absolute -bottom-2 -right-2 w-4 h-4 ${feature.accentColor} rounded-full opacity-60`}
                  ></div>
                  <div
                    className={`absolute -top-2 -left-2 w-3 h-3 ${feature.accentColor} rounded-full opacity-40`}
                  ></div>
                  <div
                    className={`absolute top-1/4 -right-4 w-2 h-2 ${feature.accentColor} rounded-full opacity-80`}
                  ></div>
                </div>
              </div>

              {/* Content Section */}
              <div className="flex-1 text-center lg:text-left max-w-lg">
                <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  {feature.title}
                </h3>

                <p
                  className={`text-lg mb-4 ${feature.color.replace(
                    "bg-",
                    "text-"
                  )}`}
                >
                  {feature.subtitle}
                </p>

                <p className="text-gray-600 text-base leading-relaxed mb-6">
                  {feature.description}
                </p>

                {/* Accent line */}
                <div
                  className={`w-16 h-1 ${feature.accentColor} rounded-full ${
                    index % 2 === 1 ? "lg:ml-auto" : "mx-auto lg:mx-0"
                  }`}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
