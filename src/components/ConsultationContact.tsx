import { MapPin, Phone, Clock, Mail, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";

export function ConsultationContact() {
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    location: "",
    program: ""
  });

  const locations = [
    {
      name: "Cơ sở Hoàn Kiếm",
      address: "Số 123 Hoàn Kiếm, Hà Nội",
      phone: "024.3636.5555",
      email: "<EMAIL>",
      hours: "Thứ 2 - Chủ nhật: 8:00 - 20:00"
    },
    {
      name: "<PERSON><PERSON> sở Ba Đình",
      address: "Số 456 Ba Đình, Hà Nội", 
      phone: "024.3737.6666",
      email: "<EMAIL>",
      hours: "Thứ 2 - Chủ nhật: 8:00 - 20:00"
    }
  ];

  const programs = [
    "<PERSON><PERSON> h<PERSON> nâng cao",
    "<PERSON><PERSON><PERSON><PERSON> thi THPT Quốc gia",
    "Ôn thi chuyển cấp",
    "Bồi dưỡng kỹ năng mềm",
    "Đà<PERSON> tạo ngoạ<PERSON> ngữ"
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Handle form submission
  };

  return (
    <section className="py-8 sm:py-12 lg:py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-3 sm:mb-4">
            Liên hệ <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">tư vấn đăng ký</span>
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto">
            Đội ngũ tư vấn viên chuyên nghiệp sẽ hỗ trợ bạn tìm ra chương trình học phù hợp nhất
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12">
          {/* Location Information */}
          <div className="space-y-4 sm:space-y-6 lg:space-y-8">
            <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6">Thông tin cơ sở</h3>
            
            {locations.map((location, index) => (
              <div key={index} className="bg-white rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg">
                <h4 className="text-lg sm:text-xl font-bold text-purple-600 mb-3 sm:mb-4">{location.name}</h4>
                
                <div className="space-y-3 sm:space-y-4">
                  <div className="flex items-start space-x-3">
                    <MapPin className="text-purple-500 mt-1" size={18} />
                    <span className="text-gray-700 text-sm sm:text-base">{location.address}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Phone className="text-green-500" size={18} />
                    <span className="text-gray-700 text-sm sm:text-base">{location.phone}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Mail className="text-blue-500" size={18} />
                    <span className="text-gray-700 text-sm sm:text-base">{location.email}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Clock className="text-orange-500" size={18} />
                    <span className="text-gray-700 text-sm sm:text-base">{location.hours}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Contact Form */}
          <div>
            <div className="bg-white rounded-3xl p-4 sm:p-6 lg:p-8 shadow-lg">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6">Đăng ký tư vấn</h3>
              
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2 text-sm sm:text-base">Họ và tên *</label>
                  <Input
                    type="text"
                    placeholder="Nhập họ và tên của bạn"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="w-full min-h-[44px]"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2 text-sm sm:text-base">Số điện thoại *</label>
                  <Input
                    type="tel"
                    placeholder="Nhập số điện thoại"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    className="w-full min-h-[44px]"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2 text-sm sm:text-base">Cơ sở quan tâm</label>
                  <select
                    className="w-full min-h-[44px] px-4 py-3 border border-gray-300 rounded-md bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm sm:text-base"
                    value={formData.location}
                    onChange={(e) => setFormData({...formData, location: e.target.value})}
                  >
                    <option value="">Chọn cơ sở</option>
                    <option value="hoankiem">Cơ sở Hoàn Kiếm</option>
                    <option value="badinh">Cơ sở Ba Đình</option>
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2 text-sm sm:text-base">Chương trình quan tâm</label>
                  <select
                    className="w-full min-h-[44px] px-4 py-3 border border-gray-300 rounded-md bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm sm:text-base"
                    value={formData.program}
                    onChange={(e) => setFormData({...formData, program: e.target.value})}
                  >
                    <option value="">Chọn chương trình</option>
                    {programs.map((program, index) => (
                      <option key={index} value={program}>{program}</option>
                    ))}
                  </select>
                </div>

                <Button 
                  type="submit"
                  className="w-full bg-[#0070F4] hover:bg-[#0059c9] text-xl h-16 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] font-bold flex items-center justify-center gap-2"
                >
                  Đăng ký ngay
                  <ArrowRight className="ml-2" size={20} />
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
