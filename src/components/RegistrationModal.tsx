import { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { GraduationCap, ArrowRight } from "lucide-react";

const registrationModalData = {
  title: "Đăng ký học thử miễn phí",
  form: {
    fields: [
      {
        name: "fullName",
        label: "Họ và tên",
        placeholder: "Nhập họ và tên của bạn",
        required: true,
      },
      {
        name: "phone",
        label: "Số điện thoại",
        placeholder: "Nhập số điện thoại",
        required: true,
      },
      {
        name: "campus",
        label: "Cơ sở quan tâm",
        type: "select",
        placeholder: "<PERSON><PERSON><PERSON> cơ sở",
        options: ["Cơ sở Cửa Bắc", "Cơ sở Lý Thường <PERSON>"],
      },
      {
        name: "program",
        label: "Chương trình quan tâm",
        type: "select",
        placeholder: "Chọn chương trình",
        options: ["Tiếng Anh giao tiếp", "IELTS/TOEFL", "Kỹ năng mềm"],
      },
    ],
    buttons: { cancel: "Hủy", submit: "Đăng ký ngay" },
  },
} as const;

interface RegistrationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function RegistrationModal({
  open,
  onOpenChange,
}: RegistrationModalProps) {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formData, setFormData] = useState({
    fullName: "",
    phone: "",
    campus: "",
    program: "",
  });
  const { toast } = useToast();

  const handleChange = (field: string, value: string) =>
    setFormData((prev) => ({ ...prev, [field]: value }));

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // Mock API call - replace with actual Supabase integration
      await new Promise((resolve) => setTimeout(resolve, 1000));

      console.log("Registration data:", formData);
      setIsSubmitted(true);

      toast({
        title: "Đăng ký thành công!",
        description: "Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.",
      });
    } catch (error) {
      toast({
        title: "Đăng ký thất bại",
        description: "Vui lòng thử lại sau.",
        variant: "destructive",
      });
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    setIsSubmitted(false);
    setFormData({
      fullName: "",
      phone: "",
      campus: "",
      program: "",
    });
  };

  if (isSubmitted) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-green-600">
              🎉 Đăng ký thành công!
            </DialogTitle>
          </DialogHeader>
          <div className="text-center py-2">
            <p className="font-medium mb-2">
              Cảm ơn bạn đã đăng ký học thử miễn phí!
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Đội ngũ tư vấn sẽ liên hệ với bạn để sắp xếp buổi học thử.
            </p>
            <Button onClick={handleClose}>Quay lại</Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-xl max-h-[90vh] overflow-y-auto sm:rounded-2xl p-8">
        <DialogHeader className="items-center mb-4">
          <div className="flex items-center gap-3">
            <GraduationCap className="w-7 h-7 text-primary" />
            <DialogTitle className="text-2xl font-bold">
              {registrationModalData.title}
            </DialogTitle>
          </div>
        </DialogHeader>

        <form onSubmit={onSubmit} className="space-y-6">
          {registrationModalData.form.fields.map((field) => {
            if (field.type === "select") {
              return (
                <div key={field.name}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {field.label}
                    {field.required && " *"}
                  </label>
                  <Select
                    value={formData[field.name as keyof typeof formData]}
                    onValueChange={(v) => handleChange(field.name, v)}
                  >
                    <SelectTrigger className="w-full min-h-[48px] border border-gray-300 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary">
                      <SelectValue placeholder={field.placeholder} />
                    </SelectTrigger>
                    <SelectContent className="rounded-lg border">
                      {field.options?.map((opt: string) => (
                        <SelectItem key={opt} value={opt} className="text-base">
                          {opt}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              );
            }
            return (
              <div key={field.name}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.label}
                  {field.required && " *"}
                </label>
                <Input
                  placeholder={field.placeholder}
                  required={field.required}
                  value={formData[field.name as keyof typeof formData]}
                  onChange={(e) => handleChange(field.name, e.target.value)}
                  className="w-full min-h-[48px] border border-gray-300 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                />
              </div>
            );
          })}

          <div className="flex justify-end gap-3 pt-4">
            <Button
              size="lg"
              variant="outline"
              onClick={handleClose}
              className="border-2 flex items-center justify-center transition-colors duration-150 hover:border-[#0059c9] hover:bg-blue-50"
            >
              {registrationModalData.form.buttons.cancel}
            </Button>
            <Button
              type="submit"
              size="lg"
              className="w-auto min-h-[48px] bg-[#0070F4] hover:bg-[#0059c9] text-white font-bold flex items-center justify-center gap-3 transition-colors duration-150 shadow-lg"
            >
              {registrationModalData.form.buttons.submit}
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
