import { TrendingUp, Users, Award, BookOpen } from "lucide-react";

export function Success() {
  const stats = [
    {
      icon: Users,
      number: "50,000+",
      label: "Học viên tin tưởng",
      color: "text-purple-600",
    },
    {
      icon: Award,
      number: "95%",
      label: "Tỷ lệ hoàn thành",
      color: "text-blue-600",
    },
    {
      icon: TrendingUp,
      number: "4.9/5",
      label: "Đánh giá trung bình",
      color: "text-teal-600",
    },
    {
      icon: BookOpen,
      number: "1,000+",
      label: "<PERSON><PERSON><PERSON> học chất lượng",
      color: "text-yellow-600",
    },
  ];

  return (
    <section className="py-16 sm:py-16 lg:py-20 bg-[#1868db] text-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          {/* <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full border border-white/20 text-sm font-medium text-white mb-6 shadow-sm">
            <div className="w-2 h-2 bg-white rounded-full"></div>
            Kết quả nổi bật
          </div> */}

          <h2 className="text-3xl lg:text-5xl font-bold text-white mb-4 lg:mb-6 leading-tight">
            Thành tựu của <span className="text-[#01FFCC]">Đức Hưng</span>
          </h2>

          <p className="text-lg lg:text-xl font-regular text-purple-100 max-w-3xl mx-auto leading-relaxed">
            Những con số ấn tượng chứng minh chất lượng giáo dục của Đức Hưng
          </p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 lg:gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-3 sm:p-4 lg:p-6 hover:bg-white/20 transition-all duration-300 h-32 sm:h-40 lg:h-48 flex flex-col justify-center items-center">
                <div className="flex justify-center mb-2 sm:mb-4">
                  <div className="bg-white/20 rounded-full p-2 sm:p-3">
                    <stat.icon size={20} className="text-white sm:w-7 sm:h-7" />
                  </div>
                </div>
                <div className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1 sm:mb-2 lg:mb-3 text-[#01FFCC]">
                  {stat.number}
                </div>
                <div className="text-purple-100 text-s sm:text-sm font-medium text-center px-1 sm:px-2 leading-tight">
                  {stat.label}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 sm:mt-12 lg:mt-20 bg-white/10 backdrop-blur-sm rounded-3xl p-4 sm:p-8 lg:p-12">
          <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center">
            <div>
              <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-4 sm:mb-6">
                Phương pháp học hiệu quả
              </h3>
              <div className="space-y-4 lg:space-y-6">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-[#01FFCC] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-purple-900 font-bold text-sm">1</span>
                  </div>
                  <div>
                    <h4 className="font-semibold lg:mb-1 text-s sm:text-base">
                      Đánh giá trình độ
                    </h4>
                    <p className="text-purple-100 text-s sm:text-sm">
                      Xác định chính xác khả năng hiện tại để đưa ra lộ trình
                      phù hợp
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-[#01FFCC] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-purple-900 font-bold text-sm">2</span>
                  </div>
                  <div>
                    <h4 className="font-semibold lg:mb-1 text-s sm:text-base">
                      Học tập cá nhân hóa
                    </h4>
                    <p className="text-purple-100 text-s sm:text-sm">
                      Chương trình học được điều chỉnh theo tốc độ và phong cách
                      học của bạn
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-[#01FFCC] rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-purple-900 font-bold text-sm">3</span>
                  </div>
                  <div>
                    <h4 className="font-semibold lg:mb-1 text-s sm:text-base">
                      Thực hành thường xuyên
                    </h4>
                    <p className="text-purple-100 text-s sm:text-sm">
                      Luyện tập với giáo viên bản ngữ và công nghệ AI tiên tiến
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 rounded-2xl p-4 sm:p-6 lg:p-8">
              <div className="text-center">
                <div className="text-3xl sm:text-4xl lg:text-6xl font-bold text-[#01FFCC] mb-1 sm:mb-2">
                  92%
                </div>
                <div className="text-base sm:text-lg lg:text-xl mb-1 sm:mb-1">
                  Học viên cải thiện đáng kể
                </div>
                <div className="text-purple-100 text-xs sm:text-sm lg:text-base">
                  sau 3 tháng học tập với Đức Hưng
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
