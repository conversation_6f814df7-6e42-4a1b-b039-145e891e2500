import { ArrowR<PERSON>, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState } from "react";

export function ConsultationForm() {
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    location: "",
    program: ""
  });

  const programs = [
    "Toán học nâng cao",
    "Luyện thi THPT Quốc gia",
    "Ôn thi chuyển cấp",
    "B<PERSON>i dưỡng kỹ năng mềm",
    "Đào tạo ngoại ngữ"
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted:", formData);
    // Handle form submission
  };

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto grid gap-14 md:grid-cols-5 px-4">
        {/* Intro */}
        <div className="md:col-span-2 flex flex-col justify-center">
          <h2 className="text-3xl md:text-4xl font-extrabold mb-6">
            Nhận tư vấn <span className="text-gradient">miễn phí</span>
          </h2>
          <p className="text-gray-600 text-lg mb-6 pr-2">
            Để lại thông tin và chuyên viên của chúng tôi sẽ liên hệ, tư vấn lộ trình học phù hợp nhất cho bạn.
          </p>

          <ul className="space-y-3 text-gray-700">
            {[
              "Cá nhân hóa theo mục tiêu",
              "Lịch học linh hoạt",
              "Ưu đãi học phí đặc biệt"].map((item) => (
              <li key={item} className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Form */}
        <div className="md:col-span-3 bg-gray-50 rounded-xl border border-gray-200 p-8 md:p-10 shadow-sm">
          <form onSubmit={handleSubmit} className="grid gap-6 md:grid-cols-2">
            <div>
              <label className="block text-gray-700 font-semibold mb-2 text-sm">Họ và tên *</label>
              <Input
                type="text"
                placeholder="Nhập họ và tên của bạn"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="w-full min-h-[52px] text-base placeholder:text-base border-2 border-gray-200 focus:border-purple-500 rounded-xl transition-all duration-200 hover:border-purple-300"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 font-semibold mb-2 text-sm">Số điện thoại *</label>
              <Input
                type="tel"
                placeholder="Nhập số điện thoại"
                value={formData.phone}
                onChange={(e) => setFormData({...formData, phone: e.target.value})}
                className="w-full min-h-[52px] text-base placeholder:text-base border-2 border-gray-200 focus:border-purple-500 rounded-xl transition-all duration-200 hover:border-purple-300"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 font-semibold mb-2 text-sm">Cơ sở quan tâm</label>
              <Select value={formData.location} onValueChange={(value) => setFormData({...formData, location: value})}>
                <SelectTrigger className="w-full min-h-[52px] text-base border-2 border-gray-200 focus:border-purple-500 rounded-xl transition-all duration-200 hover:border-purple-300">
                  <SelectValue placeholder="Chọn cơ sở" className="text-base" />
                </SelectTrigger>
                <SelectContent className="rounded-xl border-2">
                  <SelectItem value="hoankiem" className="text-base">Cơ sở Hoàn Kiếm</SelectItem>
                  <SelectItem value="badinh" className="text-base">Cơ sở Ba Đình</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-gray-700 font-semibold mb-2 text-sm">Chương trình quan tâm</label>
              <Select value={formData.program} onValueChange={(value) => setFormData({...formData, program: value})}>
                <SelectTrigger className="w-full min-h-[52px] text-base border-2 border-gray-200 focus:border-purple-500 rounded-xl transition-all duration-200 hover:border-purple-300">
                  <SelectValue placeholder="Chọn chương trình" className="text-base" />
                </SelectTrigger>
                <SelectContent className="rounded-xl border-2">
                  {programs.map((program, index) => (
                    <SelectItem key={index} value={program} className="text-base">{program}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-2">
              <Button
                type="submit"
                className="w-full bg-[#0070F4] hover:bg-[#0059c9] text-xl h-16 rounded-2xl font-bold flex items-center justify-center gap-3 shadow-lg focus:ring-4 focus:ring-primary/20"
              >
                Đăng ký ngay
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
}
