import { MapPin, Phone, Clock, Mail, Navigation } from "lucide-react";
import { useEffect, useState } from "react";
import { getCampuses, Campus } from "@/api/service/campus";

export function CampusInformation() {
  const defaultLocations: Campus[] = [
    {
      id: 1,
      name: "<PERSON><PERSON> sở Cửa Bắc",
      address: "Số 2 Cửa Bắc, Ba Đình, Hà Nội",
      phone: "024.3636.5555",
      email: "<EMAIL>",
      hours: "Thứ 2 - Chủ nhật: 8:00 - 20:00",
      mapLink: "https://www.google.com/maps/search/?api=1&query=Số+2+Cửa+Bắc,+Ba+Đ<PERSON>nh,+Hà+Nội",
    },
    {
      id: 2,
      name: "Cơ sở Lý Thường Kiệt",
      address: "Số 45 Lý Thường Kiệt, Hoàn <PERSON>ếm, Hà Nội", 
      phone: "024.3737.6666",
      email: "<EMAIL>",
      hours: "Thứ 2 - Chủ nhật: 8:00 - 20:00",
      mapLink: "https://www.google.com/maps/search/?api=1&query=Số+45+Lý+Thường+Kiệt,+Hoàn+Kiếm,+Hà+Nội",
    },
  ];
  const [locations, setLocations] = useState<Campus[]>(defaultLocations);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;
    setLoading(true);
    setError(null);
    getCampuses()
      .then((data) => {
        if (mounted) setLocations(data);
      })
      .catch(() => {
        setError("Không thể tải danh sách cơ sở. Hiển thị dữ liệu mẫu.");
        setLocations(defaultLocations);
      })
      .finally(() => setLoading(false));
    return () => { mounted = false; };
  }, []);

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-extrabold mb-4">
            Hệ thống <span className="text-gradient">cơ sở</span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto text-lg">
            Lựa chọn địa điểm thuận tiện nhất cho bạn trong hệ thống trung tâm của chúng tôi
          </p>
        </div>
        {loading ? (
          <div className="text-center py-12 text-lg text-gray-500">Đang tải danh sách cơ sở...</div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : null}
        {/* Grid of campuses */}
        <div className="grid gap-10 md:grid-cols-2">
          {locations.map((c) => (
            <article
              key={c.id}
              className="flex flex-col rounded-xl bg-white border border-gray-200 shadow-sm hover:shadow-md transition-shadow p-8"
            >
              {/* Name */}
              <header className="flex items-center gap-3 mb-6">
                <Navigation className="w-6 h-6 text-primary" />
                <h3 className="text-xl font-semibold leading-snug">{c.name}</h3>
              </header>

              <ul className="space-y-4 text-gray-700 flex-1">
                <li className="flex gap-3">
                  <MapPin className="w-5 h-5 text-primary flex-shrink-0" />
                  <span>{c.address}</span>
                </li>
                <li className="flex gap-3">
                  <Phone className="w-5 h-5 text-green-600 flex-shrink-0" />
                  <a href={`tel:${c.phone}`} className="hover:underline">{c.phone}</a>
                </li>
                <li className="flex gap-3">
                  <Mail className="w-5 h-5 text-blue-600 flex-shrink-0" />
                  <a href={`mailto:${c.email}`} className="hover:underline break-all">{c.email}</a>
                </li>
                <li className="flex gap-3">
                  <Clock className="w-5 h-5 text-orange-500 flex-shrink-0" />
                  <span>{c.hours}</span>
                </li>
              </ul>

              <a
                href={c.mapLink || `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(c.address)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="mt-6 inline-flex items-center text-sm font-medium text-primary hover:underline"
              >
                Xem bản đồ
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M17 8l4 4m0 0l-4 4m4-4H3"></path></svg>
              </a>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
}
