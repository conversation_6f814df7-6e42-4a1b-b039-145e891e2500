import {
  Facebook,
  Instagram,
  Youtube,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { scrollToTop } from "@/hooks/useScrollToTop";

export function Footer() {
  const navigate = useNavigate();

  // Helper function to navigate and scroll to top
  const navigateAndScrollToTop = (path: string) => {
    navigate(path);
    // Small delay to ensure navigation completes before scrolling
    setTimeout(() => {
      scrollToTop("smooth");
    }, 100);
  };

  return (
    <footer className="bg-[#0F4FAF] text-background">
      <div className="container mx-auto container-padding py-16">
        <div className="grid lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-2 space-y-6">
            <div
              className="flex items-center space-x-3 cursor-pointer group"
              onClick={() => navigateAndScrollToTop("/")}
            >
              <div className="w-36 h-10 flex items-center justify-center group-hover:scale-105 transition-transform">
                <img
                  src="/assets/Logo_White.svg"
                  alt="Học sinh đang học tập"
                  className="w-full h-auto p-1"
                />
              </div>
              {/* <div className="flex flex-col">
                <span className="text-2xl font-bold text-white group-hover:opacity-80 transition-opacity">
                  Đức Hưng
                </span>
                <span className="text-sm text-background/70 -mt-1">
                  Kiến tạo tương lai
                </span>
              </div> */}
            </div>
            <p className="text-white leading-relaxed max-w-md">
              Phát triển toàn diện phẩm chất và năng lực học viên với phương
              pháp giáo dục tiên tiến.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Facebook className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Instagram className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Youtube className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Truy cập nhanh</h3>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/courses")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Khóa học
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/teacher")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Giáo viên
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/blog")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Tin tức
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/about-us")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Về chúng tôi
                </button>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="flex flex-col justify-start items-start self-stretch flex-grow-0 flex-shrink-0 w-[468px] relative gap-6">
            <h3 className="self-stretch flex-grow-0 flex-shrink-0 w-[468px] text-xl font-medium text-left text-white">
              Liên hệ
            </h3>

            <div className="self-stretch flex-grow-0 flex-shrink-0 h-32 relative">
              {/* Email - Top Left */}
              <div className="flex justify-start items-start w-[225.22px] absolute left-0 top-0 gap-3">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="L22 6L12 13L2 6"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
                  <span className="text-sm font-medium text-left text-[#cecece]">
                    Email
                  </span>
                  <span className="text-base font-medium text-left text-[#fcfcfc]">
                    <EMAIL>
                  </span>
                </div>
              </div>

              {/* Phone - Top Right */}
              <div className="flex justify-start items-start w-[225.22px] absolute left-[243.22px] top-0 gap-3">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
                  <span className="text-sm font-medium text-left text-[#cecece]">
                    Điện thoại
                  </span>
                  <span className="text-base font-medium text-left text-[#fcfcfc]">
                    (123) 456 - 7890
                  </span>
                </div>
              </div>

              {/* Cửa Bắc Location - Bottom Left */}
              <div className="flex justify-start items-start w-[225.22px] absolute left-0 top-20 gap-3">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <circle
                    cx="12"
                    cy="10"
                    r="3"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                  />
                </svg>
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
                  <span className="text-sm font-medium text-left text-[#cecece]">
                    Cửa Bắc
                  </span>
                  <span className="text-base font-medium text-left text-[#fcfcfc]">
                    Số 2 Cửa Bắc, Hà Nội
                  </span>
                </div>
              </div>

              {/* Lý Thường Kiệt Location - Bottom Right */}
              <div className="flex justify-start items-start w-[225.22px] absolute left-[243.22px] top-20 gap-3">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M21 10C21 17 12 23 12 23S3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.364 3.63604C20.0518 5.32387 21 7.61305 21 10Z"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <circle
                    cx="12"
                    cy="10"
                    r="3"
                    stroke="#FCFCFC"
                    strokeWidth="2"
                  />
                </svg>
                <div className="flex flex-col justify-start items-start flex-grow-0 flex-shrink-0 relative gap-1">
                  <span className="text-sm font-medium text-left text-[#cecece]">
                    Lý Thường Kiệt
                  </span>
                  <span className="text-base font-medium text-left text-[#fcfcfc]">
                    45 Lý Thường Kiệt, Hà Nội
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-background/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white text-sm">
            © 2025 Đức Hưng. Tất cả quyền được bảo lưu.
          </p>
        </div>
      </div>
    </footer>
  );
}
