import {
  Facebook,
  Instagram,
  Youtube,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { scrollToTop } from "@/hooks/useScrollToTop";

export function Footer() {
  const navigate = useNavigate();

  // Helper function to navigate and scroll to top
  const navigateAndScrollToTop = (path: string) => {
    navigate(path);
    // Small delay to ensure navigation completes before scrolling
    setTimeout(() => {
      scrollToTop("smooth");
    }, 100);
  };

  return (
    <footer className="bg-[#1868DB] text-background">
      <div className="container mx-auto container-padding py-16">
        <div className="grid lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-2 space-y-6">
            <div
              className="flex items-center space-x-3 cursor-pointer group"
              onClick={() => navigateAndScrollToTop("/")}
            >
              <div className="w-36 h-10 flex items-center justify-center group-hover:scale-105 transition-transform">
                <img
                  src="/assets/Logo_White.svg"
                  alt="Học sinh đang học tập"
                  className="w-full h-auto p-1"
                />
              </div>
              {/* <div className="flex flex-col">
                <span className="text-2xl font-bold text-white group-hover:opacity-80 transition-opacity">
                  Đức Hưng
                </span>
                <span className="text-sm text-background/70 -mt-1">
                  Kiến tạo tương lai
                </span>
              </div> */}
            </div>
            <p className="text-background/80 leading-relaxed max-w-md">
              Trung tâm Bồi dưỡng Văn hóa và Phát triển Giáo dục Đức Hưng. Phát
              triển toàn diện phẩm chất và năng lực học viên với phương pháp
              giáo dục tiên tiến.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Facebook className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Instagram className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Youtube className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Liên kết nhanh</h3>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/courses")}
                  className="text-background/70 hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Khóa học
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/teacher")}
                  className="text-background/70 hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Giáo viên
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/blog")}
                  className="text-background/70 hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Tin tức
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/about-us")}
                  className="text-background/70 hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Về chúng tôi
                </button>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Thông tin liên hệ</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="text-white mt-1 flex-shrink-0" size={18} />
                <div className="text-background/80">
                  <p>2 Cửa Bắc, Hà Nội</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="text-white mt-1 flex-shrink-0" size={18} />
                <div className="text-background/80">
                  <p>45 Lý Thường Kiệt, Hà Nội</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="text-white flex-shrink-0" size={18} />
                <span className="text-background/80">+84 28 1234 5678</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="text-white flex-shrink-0" size={18} />
                <span className="text-background/80"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-background/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white text-sm">
            © 2025 Đức Hưng. Tất cả quyền được bảo lưu.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a
              href="#"
              className="text-white hover:text-background text-sm transition-colors"
            >
              Chính sách bảo mật
            </a>
            <a
              href="#"
              className="text-white hover:text-background text-sm transition-colors"
            >
              Điều khoản sử dụng
            </a>
            <a
              href="#"
              className="text-white hover:text-background text-sm transition-colors"
            >
              Cookies
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
