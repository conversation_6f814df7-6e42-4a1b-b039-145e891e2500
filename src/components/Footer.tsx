import {
  Facebook,
  Instagram,
  Youtube,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { scrollToTop } from "@/hooks/useScrollToTop";

export function Footer() {
  const navigate = useNavigate();

  // Helper function to navigate and scroll to top
  const navigateAndScrollToTop = (path: string) => {
    navigate(path);
    // Small delay to ensure navigation completes before scrolling
    setTimeout(() => {
      scrollToTop("smooth");
    }, 100);
  };

  return (
    <footer className="bg-[#0F4FAF] text-background">
      <div className="container mx-auto container-padding py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Company Info */}
          <div className="space-y-6">
            <div
              className="flex items-center space-x-3 cursor-pointer group"
              onClick={() => navigateAndScrollToTop("/")}
            >
              <div className="w-36 h-10 flex items-center justify-center group-hover:scale-105 transition-transform">
                <img
                  src="/assets/Logo_White.svg"
                  alt="Học sinh đang học tập"
                  className="w-full h-auto p-1"
                />
              </div>
            </div>
            <p className="text-white leading-relaxed max-w-md">
              Phát triển toàn diện phẩm chất và năng lực học viên với phương
              pháp giáo dục tiên tiến.
            </p>
            <div className="flex space-x-4">
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Facebook className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Instagram className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a
                href="#"
                className="w-10 h-10 bg-background/10 rounded-lg flex items-center justify-center hover:bg-background/20 transition-colors group"
              >
                <Youtube className="w-5 h-5 group-hover:scale-110 transition-transform" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Truy cập nhanh</h3>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/courses")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Khóa học
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/teacher")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Giáo viên
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/blog")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Tin tức
                </button>
              </li>
              <li>
                <button
                  onClick={() => navigateAndScrollToTop("/about-us")}
                  className="text-white hover:text-background transition-colors hover:translate-x-1 transform duration-200 block text-left"
                >
                  Về chúng tôi
                </button>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div className="flex flex-col gap-6">
            <h3 className="text-xl font-medium text-white">Liên hệ</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Email - Top Left */}
              <div className="flex items-start gap-3">
                <Mail className="text-white flex-shrink-0 mt-1" size={24} />
                <div className="flex flex-col gap-1">
                  <span className="text-sm font-medium text-[#cecece]">
                    Email
                  </span>
                  <span className="text-base font-medium text-[#fcfcfc]">
                    <EMAIL>
                  </span>
                </div>
              </div>

              {/* Phone - Top Right */}
              <div className="flex items-start gap-3">
                <Phone className="text-white flex-shrink-0 mt-1" size={24} />
                <div className="flex flex-col gap-1">
                  <span className="text-sm font-medium text-[#cecece]">
                    Điện thoại
                  </span>
                  <span className="text-base font-medium text-[#fcfcfc]">
                    (123) 456 - 7890
                  </span>
                </div>
              </div>

              {/* Cửa Bắc Location - Bottom Left */}
              <div className="flex items-start gap-3">
                <MapPin className="text-white flex-shrink-0 mt-1" size={24} />
                <div className="flex flex-col gap-1">
                  <span className="text-sm font-medium text-[#cecece]">
                    Cửa Bắc
                  </span>
                  <span className="text-base font-medium text-[#fcfcfc]">
                    Số 2 Cửa Bắc, Hà Nội
                  </span>
                </div>
              </div>

              {/* Lý Thường Kiệt Location - Bottom Right */}
              <div className="flex items-start gap-3">
                <MapPin className="text-white flex-shrink-0 mt-1" size={24} />
                <div className="flex flex-col gap-1">
                  <span className="text-sm font-medium text-[#cecece]">
                    Lý Thường Kiệt
                  </span>
                  <span className="text-base font-medium text-[#fcfcfc]">
                    45 Lý Thường Kiệt, Hà Nội
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-background/20 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white text-sm">
            © 2025 Đức Hưng. Tất cả quyền được bảo lưu.
          </p>
        </div>
      </div>
    </footer>
  );
}
