import { Clock, Users, ArrowRight, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import { getCourses, Course as CourseType } from "@/api/service/course";

// Unified colour for all course level badges
const BACKGROUND_COLOR = "#EFF6FF";
const LABEL_COLOR = "#0070F4";
const BORDER_COLOR = "#0070F4";

const FALLBACK_IMAGE =
  "https://images.unsplash.com/photo-1464983953574-0892a716854b?w=600&h=400&fit=crop";

const defaultCourses: CourseType[] = [];

export function Courses() {
  const navigate = useNavigate();
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [courses, setCourses] = useState<CourseType[]>(defaultCourses);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    getCourses()
      .then((data) => setCourses(data))
      .catch(() => {
        setError("Không thể tải danh sách khoá học. Hiển thị dữ liệu mẫu.");
        setCourses(defaultCourses);
      })
      .finally(() => setLoading(false));
  }, []);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api, courses]);

  // Fallback image handler
  const handleImgError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = FALLBACK_IMAGE;
  };

  return (
    <section id="courses" className="py-16 lg:py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          {/* <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
            <span className="w-2 h-2 bg-primary rounded-full"></span>
            Chương trình nổi bật
          </span> */}

          <h2 className="text-3xl lg:text-5xl font-bold mb-4 leading-tight">
            Khóa học <span className="text-gradient">chất lượng cao</span>
          </h2>
          <p className="text-md md:text-xl text-gray-600 mx-auto font-normal">
            Các khóa học được thiết kế theo tiêu chuẩn Bộ Giáo dục và đào tạo,
            phù hợp với mọi độ tuổi và trình độ
          </p>
        </div>
        {loading ? (
          <div>
            <LoadingSpinner isVisible={true} message="Đang tải..." />;
          </div>
        ) : error ? (
          <div className="text-center py-4 text-red-500">{error}</div>
        ) : null}
        <div className="max-w-6xl mx-auto px-8">
          {/* Carousel layout for all courses */}
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            setApi={setApi}
            className="w-full relative"
          >
            <CarouselContent className="-ml-2 md:-ml-4">
              {courses.map((course, idx) => (
                <CarouselItem
                  key={idx}
                  className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3"
                >
                  <div
                    className="course-card bg-white border rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 flex flex-col h-full"
                    style={
                      {
                        "--border-color": BORDER_COLOR,
                        minHeight: "540px", // Ensure consistent minimum height
                      } as React.CSSProperties
                    }
                    onMouseEnter={(e) => {
                      e.currentTarget.style.borderColor = BORDER_COLOR;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.borderColor = "#e5e7eb";
                    }}
                  >
                    {/* Image section with fixed height */}
                    <div className="relative w-full h-48 flex-shrink-0">
                      <img
                        src={course.image || FALLBACK_IMAGE}
                        alt={course.title}
                        className="object-cover w-full h-full"
                        loading="lazy"
                        onError={handleImgError}
                      />
                      {/* Subject badge on image */}
                      <span
                        className="absolute top-3 left-3 px-4 py-1 rounded-full text-sm font-semibold shadow-sm z-10 bg-white"
                        style={{
                          backgroundColor: BACKGROUND_COLOR,
                          color: LABEL_COLOR,
                          border: `2px solid ${BACKGROUND_COLOR}`,
                        }}
                      >
                        {course.level}
                      </span>
                    </div>

                    {/* Card content - flexible height */}
                    <div className="flex flex-col flex-1 p-6">
                      {/* Title */}
                      <h3 className="text-2xl font-bold text-gray-900 font-inter mb-3 line-clamp-2">
                        {course.title}
                      </h3>

                      {/* Description */}
                      <p className="text-base text-gray-600 font-inter mb-4 line-clamp-3 flex-shrink-0">
                        {course.description}
                      </p>

                      {/* Features list - grows to fill space */}
                      <div className="flex-1 mb-4">
                        <ul className="space-y-2">
                          {course.features.map((feature, i) => (
                            <li
                              key={i}
                              className="flex items-center text-gray-800 text-sm font-inter"
                            >
                              <Check className="w-4 h-4 text-[#0070F4] mr-2 flex-shrink-0" />
                              <span className="line-clamp-1">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Meta info */}
                      <div className="flex flex-wrap justify-between gap-4 text-sm text-gray-500 mb-6 font-inter flex-shrink-0">
                        <span className="flex items-center gap-1">
                          <Clock className="w-4 h-4" aria-hidden="true" />
                          {course.duration}
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="w-4 h-4" aria-hidden="true" />
                          {course.students}
                        </span>
                      </div>

                      {/* Button - always at bottom */}
                      <Button
                        className="bg-[#0070F4] hover:bg-[#0059c9] text-white font-semibold rounded-lg py-3 px-6 w-full text-base font-inter transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-[#0070F4] focus:ring-offset-2 flex items-center justify-center gap-2 mt-auto"
                        style={{ boxShadow: "none", border: "none" }}
                        tabIndex={0}
                        aria-label={`Xem chi tiết: ${course.title}`}
                        onClick={() => navigate(`/courses/${course.id}`)}
                      >
                        Xem chi tiết
                        <ArrowRight className="w-5 h-5" />
                      </Button>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="carousel-nav-btn" />
            <CarouselNext className="carousel-nav-btn" />
          </Carousel>

          {/* Dot indicators */}
          {count > 1 && (
            <div className="flex justify-center mt-8 space-x-2">
              {Array.from({ length: count }).map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index + 1 === current
                      ? "bg-[#0070F4] scale-110"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                  onClick={() => api?.scrollTo(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          )}
        </div>
        <style>{`
          .course-card {
           transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
          }
          .course-card:hover {
            border: 1px solid #0070F4;
          }
          .course-card:focus, .course-card:focus-visible {
            border: 1px solid #0070F4;
            outline: none;
          }

          /* Ensure equal heights for all cards in carousel */
          .course-card {
            display: flex;
            flex-direction: column;
            height: 100%;
          }

          /* Line clamp utilities */
          .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          /* Carousel navigation styling */
          .carousel-nav-btn {
            background: white;
            border: 2px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
          }

          .carousel-nav-btn:hover {
            border-color: #0070F4;
            background: #f8fafc;
          }

          .carousel-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          /* Responsive navigation positioning */
          @media (max-width: 768px) {
            .carousel-nav-btn {
              width: 40px;
              height: 40px;
            }
          }

          /* Responsive adjustments */
          @media (max-width: 767px) {
            .course-card {
              min-height: 520px;
            }
            .grid {
              gap: 1rem;
            }
          }

          @media (min-width: 768px) and (max-width: 1023px) {
            .course-card {
              min-height: 560px;
            }
          }

          @media (min-width: 1024px) {
            .course-card {
              min-height: 580px;
            }
          }
        `}</style>
      </div>
    </section>
  );
}
