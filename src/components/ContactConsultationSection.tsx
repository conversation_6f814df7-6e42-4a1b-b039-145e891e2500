import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CheckCircle } from "lucide-react";

const contactSectionData = {
  leftSection: {
    title: "Nhận tư vấn miễn phí",
    subtitle:
      "Để lại thông tin và chuyên viên của chúng tôi sẽ liên hệ, tư vấn lộ trình học phù hợp nhất cho bạn.",
    benefits: [
      "<PERSON><PERSON> nhân hóa theo mục tiêu",
      "<PERSON><PERSON><PERSON> học linh hoạt",
      "Ưu đãi học phí đặc biệt",
    ],
  },
  rightSection: {
    title: "Đăng ký tư vấn",
    subtitle:
      "Đội ngũ tư vấn viên chuyên nghiệp sẽ hỗ trợ bạn tìm ra chương trình phù hợp nhất",
    form: {
      fields: [
        {
          name: "fullName",
          label: "Họ và tên",
          placeholder: "<PERSON>hậ<PERSON> họ và tên của bạn",
          required: true,
        },
        {
          name: "phone",
          label: "Số điện thoại",
          placeholder: "Nhập số điện thoại",
          required: true,
        },
        {
          name: "campus",
          label: "Cơ sở quan tâm",
          type: "select",
          placeholder: "Chọn cơ sở",
          options: ["Cơ sở Cửa Bắc", "Cơ sở Lý Thường Kiệt"],
        },
        {
          name: "program",
          label: "Chương trình quan tâm",
          type: "select",
          placeholder: "Chọn chương trình",
          options: ["Tiếng Anh giao tiếp", "IELTS/TOEFL", "Kỹ năng mềm"],
        },
      ],
      submitText: "Đăng ký ngay",
    },
  },
} as const;

export function ContactConsultationSection() {
  const initial = contactSectionData.rightSection.form.fields.reduce(
    (acc: Record<string, string>, f) => {
      acc[f.name] = "";
      return acc;
    },
    {}
  );
  const [data, setData] = useState(initial);

  const onChange = (name: string, value: string) =>
    setData((prev) => ({ ...prev, [name]: value }));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("submit", data);
  };

  const { leftSection, rightSection } = contactSectionData;

  return (
    <section className="relative overflow-hidden" id="consultation">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-blue-100" />
      <div className="relative max-w-7xl mx-auto px-4 py-32 grid md:grid-cols-2 gap-12">
        {/* Left */}
        <div className="flex flex-col justify-center order-2 md:order-1">
          <span className="inline-flex items-center gap-2 px-4 py-2 bg-white rounded-full border border-gray-200 text-sm font-medium text-gray-600 mb-6 shadow-sm">
            <span className="w-2 h-2 bg-primary rounded-full" /> Tư vấn miễn phí
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
            {leftSection.title.split(" ")[0]}{" "}
            <span className="text-primary">
              {leftSection.title.split(" ").slice(1).join(" ")}
            </span>
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-md">
            {leftSection.subtitle}
          </p>
          <ul className="space-y-3 text-gray-700">
            {leftSection.benefits.map((b) => (
              <li key={b} className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                <span>{b}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Right */}
        <div className="order-1 md:order-2">
          <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl p-1">
            <div className="bg-white rounded-3xl p-8 sm:p-10 shadow-lg">
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                {rightSection.title}
              </h3>
              <p className="text-sm text-gray-600 mb-6">
                {rightSection.subtitle}
              </p>

              <form onSubmit={handleSubmit} className="space-y-5">
                {rightSection.form.fields.map((field) => {
                  const f: any = field;
                  if (f.type === "select") {
                    return (
                      <div key={f.name}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {f.label}
                          {f.required && " *"}
                        </label>
                        <Select
                          value={data[f.name]}
                          onValueChange={(v) => onChange(f.name, v)}
                        >
                          <SelectTrigger className="w-full min-h-[46px] border-2 border-gray-200 focus:border-primary rounded-lg">
                            <SelectValue placeholder={f.placeholder} />
                          </SelectTrigger>
                          <SelectContent className="rounded-lg border-2">
                            {(f.options ?? []).map((opt: string) => (
                              <SelectItem
                                key={opt}
                                value={opt}
                                className="text-base"
                              >
                                {opt}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    );
                  }
                  return (
                    <div key={f.name}>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {f.label}
                        {f.required && " *"}
                      </label>
                      <Input
                        placeholder={f.placeholder}
                        required={f.required}
                        value={data[f.name]}
                        onChange={(e) => onChange(f.name, e.target.value)}
                        className="w-full min-h-[46px] border-2 border-gray-200 focus:border-primary rounded-lg"
                      />
                    </div>
                  );
                })}

                <Button
                  size="lg"
                  type="submit"
                  className="bg-[#0070F4] hover:bg-[#0059c9] text-white font-bold flex items-center justify-center gap-3 transition-colors duration-150 shadow-lg"
                >
                  {rightSection.form.submitText}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
