# Đức Hưng ETC - K-EDU Landing Page

Trung tâm Bồi dưỡng Văn hóa và Phát triển Gi<PERSON><PERSON> dụ<PERSON> (Đức Hưng ETC) - Nền tảng gi<PERSON>o dục chấ<PERSON> l<PERSON>, ph<PERSON><PERSON> triển to<PERSON><PERSON>, kiến tạo tương lai cho học sinh Việt Nam.

## 🚀 Giới thiệu

K-EDU là website giới thiệu và đăng ký các chương trình đào tạo của Trung tâm Đức Hưng ETC, tập trung vào cá nhân hóa lộ trình học, kết hợp lý thuyết và thực hành, chuẩn quốc tế, phù hợp với mọi độ tuổi và trình độ. Học viên có thể đăng ký học thử, nhận tư vấn miễn <PERSON>h<PERSON>, xem thông tin gi<PERSON><PERSON>, <PERSON><PERSON><PERSON>, c<PERSON> sở vật chất và cảm nhận học viên.

## 🎯 Tính năng nổi bật

- **Lộ trình học cá nhân hóa**: Thiết kế riêng theo năng lực và mục tiêu học viên
- **Đội ngũ giáo viên xuất sắc**: Kinh nghiệm thực tế, chuyên môn cao
- **Chứng chỉ nhanh chóng**: Nhận chứng chỉ sau 7 ngày hoàn thành
- **Học tập linh hoạt**: Online & Offline, phù hợp lịch trình cá nhân
- **Chuẩn bị du học**: Chương trình theo tiêu chuẩn quốc tế
- **Phản hồi tức thì**: Feedback chi tiết, hỗ trợ cải thiện liên tục
- **Đăng ký học thử, nhận tư vấn miễn phí**
- **Xem lịch học, thông tin các cơ sở, liên hệ nhanh**
- **Cảm nhận học viên, đánh giá thực tế**

## 📚 Chương trình đào tạo tiêu biểu

- Ôn thi chuyển cấp, luyện thi THPT Quốc gia
- Đào tạo ngoại ngữ (Tiếng Anh giao tiếp, IELTS/TOEFL...)
- Bồi dưỡng kỹ năng mềm, kỹ năng sống
- Toán học nâng cao, lập trình cơ bản

## 🏫 Hệ thống cơ sở
- **Cơ sở Cửa Bắc**: Số 2 Cửa Bắc, Ba Đình, Hà Nội
- **Cơ sở Lý Thường Kiệt**: Số 45 Lý Thường Kiệt, Hoàn Kiếm, Hà Nội

## 👩‍💻 Công nghệ sử dụng
- [Vite](https://vitejs.dev/) + [React](https://react.dev/) + [TypeScript](https://www.typescriptlang.org/)
- [shadcn/ui](https://ui.shadcn.com/) (Radix UI, Tailwind CSS)
- [Supabase](https://supabase.com/) (tích hợp đăng ký, quản lý dữ liệu)
- [React Router](https://reactrouter.com/), [React Hook Form](https://react-hook-form.com/), [Zod](https://zod.dev/)

## ⚡️ Cài đặt & chạy dự án

Yêu cầu: Node.js >= 18, npm >= 9

```sh
# 1. Clone repository
$ git clone <YOUR_GIT_URL>
$ cd kedu-landing-page

# 2. Cài đặt dependencies
$ npm install

# 3. Chạy server phát triển
$ npm run dev
```

Truy cập: http://localhost:5173

## 📂 Cấu trúc thư mục chính
- `src/components/` - Các thành phần UI, section landing page
- `src/pages/` - Các trang chính: Trang chủ, Khóa học, Giáo viên, Tin tức, Giới thiệu...
- `src/data/` - Dữ liệu mẫu (giáo viên, blog)
- `src/integrations/supabase/` - Kết nối Supabase

## 📞 Liên hệ
- **Website:** [https://duchung.vn](https://duchung.vn)
- **Email:** <EMAIL>
- **Hotline:** +84 28 1234 5678
- **Địa chỉ:** Số 2 Cửa Bắc, Ba Đình, Hà Nội | Số 45 Lý Thường Kiệt, Hoàn Kiếm, Hà Nội

## ⭐ Đánh giá & cảm nhận học viên
- 4.9/5 điểm trung bình (1,500+ đánh giá)
- 10,000+ học viên tin tưởng
- 95% học viên sẵn sàng giới thiệu bạn bè

---

> "Chúng tôi mang đến lộ trình học tập cá nhân hóa, kết hợp lý thuyết và thực hành, được thiết kế riêng cho học sinh Việt Nam."

---

© 2025 Đức Hưng ETC. All rights reserved.
